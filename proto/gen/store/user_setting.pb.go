// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/user_setting.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserSettingKey int32

const (
	UserSettingKey_USER_SETTING_KEY_UNSPECIFIED UserSettingKey = 0
	// Access tokens for the user.
	UserSettingKey_ACCESS_TOKENS UserSettingKey = 1
	// The locale of the user.
	UserSettingKey_LOCALE UserSettingKey = 2
	// The appearance of the user.
	UserSettingKey_APPEARANCE UserSettingKey = 3
	// The visibility of the memo.
	UserSettingKey_MEMO_VISIBILITY UserSettingKey = 4
	// The shortcuts of the user.
	UserSettingKey_SHORTCUTS UserSettingKey = 5
)

// Enum value maps for UserSettingKey.
var (
	UserSettingKey_name = map[int32]string{
		0: "USER_SETTING_KEY_UNSPECIFIED",
		1: "ACCESS_TOKENS",
		2: "LOCALE",
		3: "APPEARANCE",
		4: "MEMO_VISIBILITY",
		5: "SHORTCUTS",
	}
	UserSettingKey_value = map[string]int32{
		"USER_SETTING_KEY_UNSPECIFIED": 0,
		"ACCESS_TOKENS":                1,
		"LOCALE":                       2,
		"APPEARANCE":                   3,
		"MEMO_VISIBILITY":              4,
		"SHORTCUTS":                    5,
	}
)

func (x UserSettingKey) Enum() *UserSettingKey {
	p := new(UserSettingKey)
	*p = x
	return p
}

func (x UserSettingKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserSettingKey) Descriptor() protoreflect.EnumDescriptor {
	return file_store_user_setting_proto_enumTypes[0].Descriptor()
}

func (UserSettingKey) Type() protoreflect.EnumType {
	return &file_store_user_setting_proto_enumTypes[0]
}

func (x UserSettingKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserSettingKey.Descriptor instead.
func (UserSettingKey) EnumDescriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{0}
}

type UserSetting struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	UserId int32                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Key    UserSettingKey         `protobuf:"varint,2,opt,name=key,proto3,enum=memos.store.UserSettingKey" json:"key,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*UserSetting_AccessTokens
	//	*UserSetting_Locale
	//	*UserSetting_Appearance
	//	*UserSetting_MemoVisibility
	//	*UserSetting_Shortcuts
	Value         isUserSetting_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSetting) Reset() {
	*x = UserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSetting) ProtoMessage() {}

func (x *UserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSetting.ProtoReflect.Descriptor instead.
func (*UserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{0}
}

func (x *UserSetting) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSetting) GetKey() UserSettingKey {
	if x != nil {
		return x.Key
	}
	return UserSettingKey_USER_SETTING_KEY_UNSPECIFIED
}

func (x *UserSetting) GetValue() isUserSetting_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *UserSetting) GetAccessTokens() *AccessTokensUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_AccessTokens); ok {
			return x.AccessTokens
		}
	}
	return nil
}

func (x *UserSetting) GetLocale() string {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Locale); ok {
			return x.Locale
		}
	}
	return ""
}

func (x *UserSetting) GetAppearance() string {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Appearance); ok {
			return x.Appearance
		}
	}
	return ""
}

func (x *UserSetting) GetMemoVisibility() string {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_MemoVisibility); ok {
			return x.MemoVisibility
		}
	}
	return ""
}

func (x *UserSetting) GetShortcuts() *ShortcutsUserSetting {
	if x != nil {
		if x, ok := x.Value.(*UserSetting_Shortcuts); ok {
			return x.Shortcuts
		}
	}
	return nil
}

type isUserSetting_Value interface {
	isUserSetting_Value()
}

type UserSetting_AccessTokens struct {
	AccessTokens *AccessTokensUserSetting `protobuf:"bytes,3,opt,name=access_tokens,json=accessTokens,proto3,oneof"`
}

type UserSetting_Locale struct {
	Locale string `protobuf:"bytes,4,opt,name=locale,proto3,oneof"`
}

type UserSetting_Appearance struct {
	Appearance string `protobuf:"bytes,5,opt,name=appearance,proto3,oneof"`
}

type UserSetting_MemoVisibility struct {
	MemoVisibility string `protobuf:"bytes,6,opt,name=memo_visibility,json=memoVisibility,proto3,oneof"`
}

type UserSetting_Shortcuts struct {
	Shortcuts *ShortcutsUserSetting `protobuf:"bytes,7,opt,name=shortcuts,proto3,oneof"`
}

func (*UserSetting_AccessTokens) isUserSetting_Value() {}

func (*UserSetting_Locale) isUserSetting_Value() {}

func (*UserSetting_Appearance) isUserSetting_Value() {}

func (*UserSetting_MemoVisibility) isUserSetting_Value() {}

func (*UserSetting_Shortcuts) isUserSetting_Value() {}

type AccessTokensUserSetting struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	AccessTokens  []*AccessTokensUserSetting_AccessToken `protobuf:"bytes,1,rep,name=access_tokens,json=accessTokens,proto3" json:"access_tokens,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccessTokensUserSetting) Reset() {
	*x = AccessTokensUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccessTokensUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTokensUserSetting) ProtoMessage() {}

func (x *AccessTokensUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTokensUserSetting.ProtoReflect.Descriptor instead.
func (*AccessTokensUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{1}
}

func (x *AccessTokensUserSetting) GetAccessTokens() []*AccessTokensUserSetting_AccessToken {
	if x != nil {
		return x.AccessTokens
	}
	return nil
}

type ShortcutsUserSetting struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Shortcuts     []*ShortcutsUserSetting_Shortcut `protobuf:"bytes,1,rep,name=shortcuts,proto3" json:"shortcuts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShortcutsUserSetting) Reset() {
	*x = ShortcutsUserSetting{}
	mi := &file_store_user_setting_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShortcutsUserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShortcutsUserSetting) ProtoMessage() {}

func (x *ShortcutsUserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShortcutsUserSetting.ProtoReflect.Descriptor instead.
func (*ShortcutsUserSetting) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{2}
}

func (x *ShortcutsUserSetting) GetShortcuts() []*ShortcutsUserSetting_Shortcut {
	if x != nil {
		return x.Shortcuts
	}
	return nil
}

type AccessTokensUserSetting_AccessToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The access token is a JWT token.
	// Including expiration time, issuer, etc.
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// A description for the access token.
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccessTokensUserSetting_AccessToken) Reset() {
	*x = AccessTokensUserSetting_AccessToken{}
	mi := &file_store_user_setting_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccessTokensUserSetting_AccessToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTokensUserSetting_AccessToken) ProtoMessage() {}

func (x *AccessTokensUserSetting_AccessToken) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTokensUserSetting_AccessToken.ProtoReflect.Descriptor instead.
func (*AccessTokensUserSetting_AccessToken) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{1, 0}
}

func (x *AccessTokensUserSetting_AccessToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *AccessTokensUserSetting_AccessToken) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ShortcutsUserSetting_Shortcut struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Filter        string                 `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShortcutsUserSetting_Shortcut) Reset() {
	*x = ShortcutsUserSetting_Shortcut{}
	mi := &file_store_user_setting_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShortcutsUserSetting_Shortcut) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShortcutsUserSetting_Shortcut) ProtoMessage() {}

func (x *ShortcutsUserSetting_Shortcut) ProtoReflect() protoreflect.Message {
	mi := &file_store_user_setting_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShortcutsUserSetting_Shortcut.ProtoReflect.Descriptor instead.
func (*ShortcutsUserSetting_Shortcut) Descriptor() ([]byte, []int) {
	return file_store_user_setting_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ShortcutsUserSetting_Shortcut) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ShortcutsUserSetting_Shortcut) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ShortcutsUserSetting_Shortcut) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

var File_store_user_setting_proto protoreflect.FileDescriptor

const file_store_user_setting_proto_rawDesc = "" +
	"\n" +
	"\x18store/user_setting.proto\x12\vmemos.store\"\xd5\x02\n" +
	"\vUserSetting\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x05R\x06userId\x12-\n" +
	"\x03key\x18\x02 \x01(\x0e2\x1b.memos.store.UserSettingKeyR\x03key\x12K\n" +
	"\raccess_tokens\x18\x03 \x01(\v2$.memos.store.AccessTokensUserSettingH\x00R\faccessTokens\x12\x18\n" +
	"\x06locale\x18\x04 \x01(\tH\x00R\x06locale\x12 \n" +
	"\n" +
	"appearance\x18\x05 \x01(\tH\x00R\n" +
	"appearance\x12)\n" +
	"\x0fmemo_visibility\x18\x06 \x01(\tH\x00R\x0ememoVisibility\x12A\n" +
	"\tshortcuts\x18\a \x01(\v2!.memos.store.ShortcutsUserSettingH\x00R\tshortcutsB\a\n" +
	"\x05value\"\xc4\x01\n" +
	"\x17AccessTokensUserSetting\x12U\n" +
	"\raccess_tokens\x18\x01 \x03(\v20.memos.store.AccessTokensUserSetting.AccessTokenR\faccessTokens\x1aR\n" +
	"\vAccessToken\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"\xaa\x01\n" +
	"\x14ShortcutsUserSetting\x12H\n" +
	"\tshortcuts\x18\x01 \x03(\v2*.memos.store.ShortcutsUserSetting.ShortcutR\tshortcuts\x1aH\n" +
	"\bShortcut\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x16\n" +
	"\x06filter\x18\x03 \x01(\tR\x06filter*\x85\x01\n" +
	"\x0eUserSettingKey\x12 \n" +
	"\x1cUSER_SETTING_KEY_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rACCESS_TOKENS\x10\x01\x12\n" +
	"\n" +
	"\x06LOCALE\x10\x02\x12\x0e\n" +
	"\n" +
	"APPEARANCE\x10\x03\x12\x13\n" +
	"\x0fMEMO_VISIBILITY\x10\x04\x12\r\n" +
	"\tSHORTCUTS\x10\x05B\x9b\x01\n" +
	"\x0fcom.memos.storeB\x10UserSettingProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_user_setting_proto_rawDescOnce sync.Once
	file_store_user_setting_proto_rawDescData []byte
)

func file_store_user_setting_proto_rawDescGZIP() []byte {
	file_store_user_setting_proto_rawDescOnce.Do(func() {
		file_store_user_setting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_user_setting_proto_rawDesc), len(file_store_user_setting_proto_rawDesc)))
	})
	return file_store_user_setting_proto_rawDescData
}

var file_store_user_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_store_user_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_store_user_setting_proto_goTypes = []any{
	(UserSettingKey)(0),                         // 0: memos.store.UserSettingKey
	(*UserSetting)(nil),                         // 1: memos.store.UserSetting
	(*AccessTokensUserSetting)(nil),             // 2: memos.store.AccessTokensUserSetting
	(*ShortcutsUserSetting)(nil),                // 3: memos.store.ShortcutsUserSetting
	(*AccessTokensUserSetting_AccessToken)(nil), // 4: memos.store.AccessTokensUserSetting.AccessToken
	(*ShortcutsUserSetting_Shortcut)(nil),       // 5: memos.store.ShortcutsUserSetting.Shortcut
}
var file_store_user_setting_proto_depIdxs = []int32{
	0, // 0: memos.store.UserSetting.key:type_name -> memos.store.UserSettingKey
	2, // 1: memos.store.UserSetting.access_tokens:type_name -> memos.store.AccessTokensUserSetting
	3, // 2: memos.store.UserSetting.shortcuts:type_name -> memos.store.ShortcutsUserSetting
	4, // 3: memos.store.AccessTokensUserSetting.access_tokens:type_name -> memos.store.AccessTokensUserSetting.AccessToken
	5, // 4: memos.store.ShortcutsUserSetting.shortcuts:type_name -> memos.store.ShortcutsUserSetting.Shortcut
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_store_user_setting_proto_init() }
func file_store_user_setting_proto_init() {
	if File_store_user_setting_proto != nil {
		return
	}
	file_store_user_setting_proto_msgTypes[0].OneofWrappers = []any{
		(*UserSetting_AccessTokens)(nil),
		(*UserSetting_Locale)(nil),
		(*UserSetting_Appearance)(nil),
		(*UserSetting_MemoVisibility)(nil),
		(*UserSetting_Shortcuts)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_user_setting_proto_rawDesc), len(file_store_user_setting_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_user_setting_proto_goTypes,
		DependencyIndexes: file_store_user_setting_proto_depIdxs,
		EnumInfos:         file_store_user_setting_proto_enumTypes,
		MessageInfos:      file_store_user_setting_proto_msgTypes,
	}.Build()
	File_store_user_setting_proto = out.File
	file_store_user_setting_proto_goTypes = nil
	file_store_user_setting_proto_depIdxs = nil
}
