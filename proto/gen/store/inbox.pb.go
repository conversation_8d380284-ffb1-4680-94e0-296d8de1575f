// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: store/inbox.proto

package store

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InboxMessage_Type int32

const (
	InboxMessage_TYPE_UNSPECIFIED InboxMessage_Type = 0
	InboxMessage_MEMO_COMMENT     InboxMessage_Type = 1
	InboxMessage_VERSION_UPDATE   InboxMessage_Type = 2
)

// Enum value maps for InboxMessage_Type.
var (
	InboxMessage_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MEMO_COMMENT",
		2: "VERSION_UPDATE",
	}
	InboxMessage_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MEMO_COMMENT":     1,
		"VERSION_UPDATE":   2,
	}
)

func (x InboxMessage_Type) Enum() *InboxMessage_Type {
	p := new(InboxMessage_Type)
	*p = x
	return p
}

func (x InboxMessage_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InboxMessage_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_store_inbox_proto_enumTypes[0].Descriptor()
}

func (InboxMessage_Type) Type() protoreflect.EnumType {
	return &file_store_inbox_proto_enumTypes[0]
}

func (x InboxMessage_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InboxMessage_Type.Descriptor instead.
func (InboxMessage_Type) EnumDescriptor() ([]byte, []int) {
	return file_store_inbox_proto_rawDescGZIP(), []int{0, 0}
}

type InboxMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          InboxMessage_Type      `protobuf:"varint,1,opt,name=type,proto3,enum=memos.store.InboxMessage_Type" json:"type,omitempty"`
	ActivityId    *int32                 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3,oneof" json:"activity_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InboxMessage) Reset() {
	*x = InboxMessage{}
	mi := &file_store_inbox_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InboxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InboxMessage) ProtoMessage() {}

func (x *InboxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_store_inbox_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InboxMessage.ProtoReflect.Descriptor instead.
func (*InboxMessage) Descriptor() ([]byte, []int) {
	return file_store_inbox_proto_rawDescGZIP(), []int{0}
}

func (x *InboxMessage) GetType() InboxMessage_Type {
	if x != nil {
		return x.Type
	}
	return InboxMessage_TYPE_UNSPECIFIED
}

func (x *InboxMessage) GetActivityId() int32 {
	if x != nil && x.ActivityId != nil {
		return *x.ActivityId
	}
	return 0
}

var File_store_inbox_proto protoreflect.FileDescriptor

const file_store_inbox_proto_rawDesc = "" +
	"\n" +
	"\x11store/inbox.proto\x12\vmemos.store\"\xbc\x01\n" +
	"\fInboxMessage\x122\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1e.memos.store.InboxMessage.TypeR\x04type\x12$\n" +
	"\vactivity_id\x18\x02 \x01(\x05H\x00R\n" +
	"activityId\x88\x01\x01\"B\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fMEMO_COMMENT\x10\x01\x12\x12\n" +
	"\x0eVERSION_UPDATE\x10\x02B\x0e\n" +
	"\f_activity_idB\x95\x01\n" +
	"\x0fcom.memos.storeB\n" +
	"InboxProtoP\x01Z)github.com/usememos/memos/proto/gen/store\xa2\x02\x03MSX\xaa\x02\vMemos.Store\xca\x02\vMemos\\Store\xe2\x02\x17Memos\\Store\\GPBMetadata\xea\x02\fMemos::Storeb\x06proto3"

var (
	file_store_inbox_proto_rawDescOnce sync.Once
	file_store_inbox_proto_rawDescData []byte
)

func file_store_inbox_proto_rawDescGZIP() []byte {
	file_store_inbox_proto_rawDescOnce.Do(func() {
		file_store_inbox_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_store_inbox_proto_rawDesc), len(file_store_inbox_proto_rawDesc)))
	})
	return file_store_inbox_proto_rawDescData
}

var file_store_inbox_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_store_inbox_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_store_inbox_proto_goTypes = []any{
	(InboxMessage_Type)(0), // 0: memos.store.InboxMessage.Type
	(*InboxMessage)(nil),   // 1: memos.store.InboxMessage
}
var file_store_inbox_proto_depIdxs = []int32{
	0, // 0: memos.store.InboxMessage.type:type_name -> memos.store.InboxMessage.Type
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_store_inbox_proto_init() }
func file_store_inbox_proto_init() {
	if File_store_inbox_proto != nil {
		return
	}
	file_store_inbox_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_store_inbox_proto_rawDesc), len(file_store_inbox_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_store_inbox_proto_goTypes,
		DependencyIndexes: file_store_inbox_proto_depIdxs,
		EnumInfos:         file_store_inbox_proto_enumTypes,
		MessageInfos:      file_store_inbox_proto_msgTypes,
	}.Build()
	File_store_inbox_proto = out.File
	file_store_inbox_proto_goTypes = nil
	file_store_inbox_proto_depIdxs = nil
}
