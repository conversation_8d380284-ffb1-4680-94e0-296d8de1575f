swagger: "2.0"
info:
  title: api/v1/activity_service.proto
  version: version not set
tags:
  - name: ActivityService
  - name: UserService
  - name: AuthService
  - name: IdentityProviderService
  - name: InboxService
  - name: MarkdownService
  - name: ResourceService
  - name: MemoService
  - name: WebhookService
  - name: WorkspaceService
  - name: WorkspaceSettingService
consumes:
  - application/json
produces:
  - application/json
paths:
  /api/v1/auth/signin:
    post:
      summary: SignIn signs in the user.
      operationId: AuthService_SignIn
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: passwordCredentials.username
          description: The username to sign in with.
          in: query
          required: false
          type: string
        - name: passwordCredentials.password
          description: The password to sign in with.
          in: query
          required: false
          type: string
        - name: ssoCredentials.idpId
          description: The ID of the SSO provider.
          in: query
          required: false
          type: integer
          format: int32
        - name: ssoCredentials.code
          description: The code to sign in with.
          in: query
          required: false
          type: string
        - name: ssoCredentials.redirectUri
          description: The redirect URI.
          in: query
          required: false
          type: string
        - name: neverExpire
          description: Whether the session should never expire.
          in: query
          required: false
          type: boolean
      tags:
        - AuthService
  /api/v1/auth/signout:
    post:
      summary: SignOut signs out the user.
      operationId: AuthService_SignOut
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - AuthService
  /api/v1/auth/signup:
    post:
      summary: SignUp signs up the user with the given username and password.
      operationId: AuthService_SignUp
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: username
          description: The username to sign up with.
          in: query
          required: false
          type: string
        - name: password
          description: The password to sign up with.
          in: query
          required: false
          type: string
      tags:
        - AuthService
  /api/v1/auth/status:
    post:
      summary: GetAuthStatus returns the current auth status of the user.
      operationId: AuthService_GetAuthStatus
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - AuthService
  /api/v1/identityProviders:
    get:
      summary: ListIdentityProviders lists identity providers.
      operationId: IdentityProviderService_ListIdentityProviders
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListIdentityProvidersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - IdentityProviderService
    post:
      summary: CreateIdentityProvider creates an identity provider.
      operationId: IdentityProviderService_CreateIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: identityProvider
          description: The identityProvider to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
      tags:
        - IdentityProviderService
  /api/v1/inboxes:
    get:
      summary: ListInboxes lists inboxes for a user.
      operationId: InboxService_ListInboxes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListInboxesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: user
          description: 'Format: users/{user}'
          in: query
          required: false
          type: string
        - name: pageSize
          description: The maximum number of inbox to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
      tags:
        - InboxService
  /api/v1/markdown/link:metadata:
    get:
      summary: GetLinkMetadata returns metadata for a given link.
      operationId: MarkdownService_GetLinkMetadata
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1LinkMetadata'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: link
          in: query
          required: false
          type: string
      tags:
        - MarkdownService
  /api/v1/markdown/node:restore:
    post:
      summary: RestoreMarkdownNodes restores the given nodes to markdown content.
      operationId: MarkdownService_RestoreMarkdownNodes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1RestoreMarkdownNodesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1RestoreMarkdownNodesRequest'
      tags:
        - MarkdownService
  /api/v1/markdown/node:stringify:
    post:
      summary: StringifyMarkdownNodes stringify the given nodes to plain text content.
      operationId: MarkdownService_StringifyMarkdownNodes
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1StringifyMarkdownNodesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1StringifyMarkdownNodesRequest'
      tags:
        - MarkdownService
  /api/v1/markdown:parse:
    post:
      summary: ParseMarkdown parses the given markdown content and returns a list of nodes.
      operationId: MarkdownService_ParseMarkdown
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ParseMarkdownResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1ParseMarkdownRequest'
      tags:
        - MarkdownService
  /api/v1/memos:
    get:
      summary: ListMemos lists memos with pagination and filter.
      operationId: MemoService_ListMemos
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemosResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            The parent is the owner of the memos.
            If not specified or `users/-`, it will list all memos.
          in: query
          required: false
          type: string
        - name: pageSize
          description: The maximum number of memos to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            A page token, received from a previous `ListMemos` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: state
          description: |-
            The state of the memos to list.
            Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
          in: query
          required: false
          type: string
          enum:
            - STATE_UNSPECIFIED
            - NORMAL
            - ARCHIVED
          default: STATE_UNSPECIFIED
        - name: sort
          description: |-
            What field to sort the results by.
            Default to display_time.
          in: query
          required: false
          type: string
        - name: direction
          description: |-
            The direction to sort the results by.
            Default to DESC.
          in: query
          required: false
          type: string
          enum:
            - DIRECTION_UNSPECIFIED
            - ASC
            - DESC
          default: DIRECTION_UNSPECIFIED
        - name: filter
          description: |-
            Filter is a CEL expression to filter memos.
            Refer to `Shortcut.filter`.
          in: query
          required: false
          type: string
        - name: oldFilter
          description: |-
            [Deprecated] Old filter contains some specific conditions to filter memos.
            Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
          in: query
          required: false
          type: string
      tags:
        - MemoService
    post:
      summary: CreateMemo creates a memo.
      operationId: MemoService_CreateMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: memo
          description: The memo to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Memo'
            required:
              - memo
      tags:
        - MemoService
  /api/v1/reactions/{id}:
    delete:
      summary: DeleteMemoReaction deletes a reaction for a memo.
      operationId: MemoService_DeleteMemoReaction
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: id
          description: |-
            The id of the reaction.
            Refer to the `Reaction.id`.
          in: path
          required: true
          type: integer
          format: int32
      tags:
        - MemoService
  /api/v1/resources:
    get:
      summary: ListResources lists all resources.
      operationId: ResourceService_ListResources
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListResourcesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - ResourceService
    post:
      summary: CreateResource creates a new resource.
      operationId: ResourceService_CreateResource
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Resource'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: resource
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1Resource'
      tags:
        - ResourceService
  /api/v1/users:
    get:
      summary: ListUsers returns a list of users.
      operationId: UserService_ListUsers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListUsersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - UserService
    post:
      summary: CreateUser creates a new user.
      operationId: UserService_CreateUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: user
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1User'
      tags:
        - UserService
  /api/v1/users/-/stats:
    post:
      summary: ListAllUserStats returns all user stats.
      operationId: UserService_ListAllUserStats
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListAllUserStatsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - UserService
  /api/v1/users:username:
    get:
      summary: GetUserByUsername gets a user by username.
      operationId: UserService_GetUserByUsername
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: username
          description: The username of the user.
          in: query
          required: false
          type: string
      tags:
        - UserService
  /api/v1/webhooks:
    get:
      summary: ListWebhooks returns a list of webhooks.
      operationId: WebhookService_ListWebhooks
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListWebhooksResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: creator
          description: The name of the creator.
          in: query
          required: false
          type: string
      tags:
        - WebhookService
    post:
      summary: CreateWebhook creates a new webhook.
      operationId: WebhookService_CreateWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/v1CreateWebhookRequest'
      tags:
        - WebhookService
  /api/v1/webhooks/{id}:
    get:
      summary: GetWebhook returns a webhook by id.
      operationId: WebhookService_GetWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: id
          in: path
          required: true
          type: integer
          format: int32
      tags:
        - WebhookService
    delete:
      summary: DeleteWebhook deletes a webhook by id.
      operationId: WebhookService_DeleteWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: id
          in: path
          required: true
          type: integer
          format: int32
      tags:
        - WebhookService
  /api/v1/webhooks/{webhook.id}:
    patch:
      summary: UpdateWebhook updates a webhook.
      operationId: WebhookService_UpdateWebhook
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Webhook'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: webhook.id
          in: path
          required: true
          type: integer
          format: int32
        - name: webhook
          in: body
          required: true
          schema:
            type: object
            properties:
              creator:
                type: string
                description: The name of the creator.
              createTime:
                type: string
                format: date-time
              updateTime:
                type: string
                format: date-time
              name:
                type: string
              url:
                type: string
      tags:
        - WebhookService
  /api/v1/workspace/profile:
    get:
      summary: GetWorkspaceProfile returns the workspace profile.
      operationId: WorkspaceService_GetWorkspaceProfile
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1WorkspaceProfile'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      tags:
        - WorkspaceService
  /api/v1/workspace/{name}:
    get:
      summary: GetWorkspaceSetting returns the setting by name.
      operationId: WorkspaceSettingService_GetWorkspaceSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1WorkspaceSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            The resource name of the workspace setting.
            Format: settings/{setting}
          in: path
          required: true
          type: string
          pattern: settings/[^/]+
      tags:
        - WorkspaceSettingService
  /api/v1/workspace/{setting.name}:
    patch:
      summary: SetWorkspaceSetting updates the setting.
      operationId: WorkspaceSettingService_SetWorkspaceSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1WorkspaceSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: setting.name
          description: |-
            name is the name of the setting.
            Format: settings/{setting}
          in: path
          required: true
          type: string
          pattern: settings/[^/]+
        - name: setting
          description: setting is the setting to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              generalSetting:
                $ref: '#/definitions/apiv1WorkspaceGeneralSetting'
              storageSetting:
                $ref: '#/definitions/apiv1WorkspaceStorageSetting'
              memoRelatedSetting:
                $ref: '#/definitions/apiv1WorkspaceMemoRelatedSetting'
            title: setting is the setting to update.
      tags:
        - WorkspaceSettingService
  /api/v1/{identityProvider.name}:
    patch:
      summary: UpdateIdentityProvider updates an identity provider.
      operationId: IdentityProviderService_UpdateIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: identityProvider.name
          description: |-
            The name of the identityProvider.
            Format: identityProviders/{id}, id is the system generated auto-incremented id.
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
        - name: identityProvider
          description: The identityProvider to update.
          in: body
          required: true
          schema:
            type: object
            properties:
              type:
                $ref: '#/definitions/apiv1IdentityProviderType'
              title:
                type: string
              identifierFilter:
                type: string
              config:
                $ref: '#/definitions/apiv1IdentityProviderConfig'
            title: The identityProvider to update.
      tags:
        - IdentityProviderService
  /api/v1/{inbox.name}:
    patch:
      summary: UpdateInbox updates an inbox.
      operationId: InboxService_UpdateInbox
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Inbox'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: inbox.name
          description: |-
            The name of the inbox.
            Format: inboxes/{id}, id is the system generated auto-incremented id.
          in: path
          required: true
          type: string
          pattern: inboxes/[^/]+
        - name: inbox
          in: body
          required: true
          schema:
            type: object
            properties:
              sender:
                type: string
                title: 'Format: users/{user}'
              receiver:
                type: string
                title: 'Format: users/{user}'
              status:
                $ref: '#/definitions/v1InboxStatus'
              createTime:
                type: string
                format: date-time
              type:
                $ref: '#/definitions/v1InboxType'
              activityId:
                type: integer
                format: int32
      tags:
        - InboxService
  /api/v1/{memo.name}:
    patch:
      summary: UpdateMemo updates a memo.
      operationId: MemoService_UpdateMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: memo.name
          description: |-
            The name of the memo.
            Format: memos/{memo}, memo is the user defined id or uuid.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: memo
          description: |-
            The memo to update.
            The `name` field is required.
          in: body
          required: true
          schema:
            type: object
            properties:
              state:
                $ref: '#/definitions/v1State'
              creator:
                type: string
                title: |-
                  The name of the creator.
                  Format: users/{user}
              createTime:
                type: string
                format: date-time
              updateTime:
                type: string
                format: date-time
              displayTime:
                type: string
                format: date-time
              content:
                type: string
              nodes:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Node'
                readOnly: true
              visibility:
                $ref: '#/definitions/v1Visibility'
              tags:
                type: array
                items:
                  type: string
                readOnly: true
              pinned:
                type: boolean
              resources:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Resource'
              relations:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1MemoRelation'
              reactions:
                type: array
                items:
                  type: object
                  $ref: '#/definitions/v1Reaction'
                readOnly: true
              property:
                $ref: '#/definitions/v1MemoProperty'
                readOnly: true
              parent:
                type: string
                title: |-
                  The name of the parent memo.
                  Format: memos/{id}
                readOnly: true
              snippet:
                type: string
                description: The snippet of the memo content. Plain text only.
                readOnly: true
              location:
                $ref: '#/definitions/apiv1Location'
                description: The location of the memo.
            title: |-
              The memo to update.
              The `name` field is required.
            required:
              - memo
      tags:
        - MemoService
  /api/v1/{name_1}:
    get:
      summary: GetUser gets a user by name.
      operationId: UserService_GetUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_1
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
    delete:
      summary: DeleteIdentityProvider deletes an identity provider.
      operationId: IdentityProviderService_DeleteIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_1
          description: The name of the identityProvider to delete.
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
      tags:
        - IdentityProviderService
  /api/v1/{name_2}:
    get:
      summary: GetIdentityProvider gets an identity provider.
      operationId: IdentityProviderService_GetIdentityProvider
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1IdentityProvider'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_2
          description: The name of the identityProvider to get.
          in: path
          required: true
          type: string
          pattern: identityProviders/[^/]+
      tags:
        - IdentityProviderService
    delete:
      summary: DeleteInbox deletes an inbox.
      operationId: InboxService_DeleteInbox
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_2
          description: The name of the inbox to delete.
          in: path
          required: true
          type: string
          pattern: inboxes/[^/]+
      tags:
        - InboxService
  /api/v1/{name_3}:
    get:
      summary: GetResource returns a resource by name.
      operationId: ResourceService_GetResource
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Resource'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_3
          description: The name of the resource.
          in: path
          required: true
          type: string
          pattern: resources/[^/]+
      tags:
        - ResourceService
    delete:
      summary: DeleteResource deletes a resource by name.
      operationId: ResourceService_DeleteResource
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_3
          description: The name of the resource.
          in: path
          required: true
          type: string
          pattern: resources/[^/]+
      tags:
        - ResourceService
  /api/v1/{name_4}:
    get:
      summary: GetMemo gets a memo.
      operationId: MemoService_GetMemo
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_4
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
    delete:
      summary: DeleteMemo deletes a memo.
      operationId: MemoService_DeleteMemo
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name_4
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
  /api/v1/{name}:
    get:
      summary: GetActivity returns the activity with the given id.
      operationId: ActivityService_GetActivity
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Activity'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: |-
            The name of the activity.
            Format: activities/{id}, id is the system generated auto-incremented id.
          in: path
          required: true
          type: string
          pattern: activities/[^/]+
      tags:
        - ActivityService
    delete:
      summary: DeleteUser deletes a user.
      operationId: UserService_DeleteUser
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{name}/access_tokens:
    get:
      summary: ListUserAccessTokens returns a list of access tokens for a user.
      operationId: UserService_ListUserAccessTokens
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListUserAccessTokensResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
    post:
      summary: CreateUserAccessToken creates a new access token for a user.
      operationId: UserService_CreateUserAccessToken
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1UserAccessToken'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UserServiceCreateUserAccessTokenBody'
      tags:
        - UserService
  /api/v1/{name}/access_tokens/{accessToken}:
    delete:
      summary: DeleteUserAccessToken deletes an access token for a user.
      operationId: UserService_DeleteUserAccessToken
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: accessToken
          description: access_token is the access token to delete.
          in: path
          required: true
          type: string
      tags:
        - UserService
  /api/v1/{name}/comments:
    get:
      summary: ListMemoComments lists comments for a memo.
      operationId: MemoService_ListMemoComments
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoCommentsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
    post:
      summary: CreateMemoComment creates a comment for a memo.
      operationId: MemoService_CreateMemoComment
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Memo'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: comment
          description: The comment to create.
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Memo'
      tags:
        - MemoService
  /api/v1/{name}/reactions:
    get:
      summary: ListMemoReactions lists reactions for a memo.
      operationId: MemoService_ListMemoReactions
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoReactionsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
    post:
      summary: UpsertMemoReaction upserts a reaction for a memo.
      operationId: MemoService_UpsertMemoReaction
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Reaction'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceUpsertMemoReactionBody'
      tags:
        - MemoService
  /api/v1/{name}/relations:
    get:
      summary: ListMemoRelations lists relations for a memo.
      operationId: MemoService_ListMemoRelations
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoRelationsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
    patch:
      summary: SetMemoRelations sets relations for a memo.
      operationId: MemoService_SetMemoRelations
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceSetMemoRelationsBody'
      tags:
        - MemoService
  /api/v1/{name}/resources:
    get:
      summary: ListMemoResources lists resources for a memo.
      operationId: MemoService_ListMemoResources
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemoResourcesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
      tags:
        - MemoService
    patch:
      summary: SetMemoResources sets resources for a memo.
      operationId: MemoService_SetMemoResources
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the memo.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceSetMemoResourcesBody'
      tags:
        - MemoService
  /api/v1/{name}/setting:
    get:
      summary: GetUserSetting gets the setting of a user.
      operationId: UserService_GetUserSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1UserSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{name}/stats:
    get:
      summary: GetUserStats returns the stats of a user.
      operationId: UserService_GetUserStats
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1UserStats'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
  /api/v1/{parent}/memos:
    get:
      summary: ListMemos lists memos with pagination and filter.
      operationId: MemoService_ListMemos2
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListMemosResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            The parent is the owner of the memos.
            If not specified or `users/-`, it will list all memos.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: pageSize
          description: The maximum number of memos to return.
          in: query
          required: false
          type: integer
          format: int32
        - name: pageToken
          description: |-
            A page token, received from a previous `ListMemos` call.
            Provide this to retrieve the subsequent page.
          in: query
          required: false
          type: string
        - name: state
          description: |-
            The state of the memos to list.
            Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
          in: query
          required: false
          type: string
          enum:
            - STATE_UNSPECIFIED
            - NORMAL
            - ARCHIVED
          default: STATE_UNSPECIFIED
        - name: sort
          description: |-
            What field to sort the results by.
            Default to display_time.
          in: query
          required: false
          type: string
        - name: direction
          description: |-
            The direction to sort the results by.
            Default to DESC.
          in: query
          required: false
          type: string
          enum:
            - DIRECTION_UNSPECIFIED
            - ASC
            - DESC
          default: DIRECTION_UNSPECIFIED
        - name: filter
          description: |-
            Filter is a CEL expression to filter memos.
            Refer to `Shortcut.filter`.
          in: query
          required: false
          type: string
        - name: oldFilter
          description: |-
            [Deprecated] Old filter contains some specific conditions to filter memos.
            Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
          in: query
          required: false
          type: string
      tags:
        - MemoService
  /api/v1/{parent}/shortcuts:
    get:
      summary: ListShortcuts returns a list of shortcuts for a user.
      operationId: UserService_ListShortcuts
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1ListShortcutsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
      tags:
        - UserService
    post:
      summary: CreateShortcut creates a new shortcut for a user.
      operationId: UserService_CreateShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: shortcut
          in: body
          required: true
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        - name: validateOnly
          in: query
          required: false
          type: boolean
      tags:
        - UserService
  /api/v1/{parent}/shortcuts/{id}:
    delete:
      summary: DeleteShortcut deletes a shortcut for a user.
      operationId: UserService_DeleteShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: id
          description: The id of the shortcut.
          in: path
          required: true
          type: string
      tags:
        - UserService
  /api/v1/{parent}/shortcuts/{shortcut.id}:
    patch:
      summary: UpdateShortcut updates a shortcut for a user.
      operationId: UserService_UpdateShortcut
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1Shortcut'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: shortcut.id
          in: path
          required: true
          type: string
        - name: shortcut
          in: body
          required: true
          schema:
            type: object
            properties:
              title:
                type: string
              filter:
                type: string
      tags:
        - UserService
  /api/v1/{parent}/tags/{tag}:
    delete:
      summary: DeleteMemoTag deletes a tag for a memo.
      operationId: MemoService_DeleteMemoTag
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            The parent, who owns the tags.
            Format: memos/{id}. Use "memos/-" to delete all tags.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: tag
          in: path
          required: true
          type: string
        - name: deleteRelatedMemos
          in: query
          required: false
          type: boolean
      tags:
        - MemoService
  /api/v1/{parent}/tags:rename:
    patch:
      summary: RenameMemoTag renames a tag for a memo.
      operationId: MemoService_RenameMemoTag
      responses:
        "200":
          description: A successful response.
          schema:
            type: object
            properties: {}
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: parent
          description: |-
            The parent, who owns the tags.
            Format: memos/{id}. Use "memos/-" to rename all tags.
          in: path
          required: true
          type: string
          pattern: memos/[^/]+
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemoServiceRenameMemoTagBody'
      tags:
        - MemoService
  /api/v1/{resource.name}:
    patch:
      summary: UpdateResource updates a resource.
      operationId: ResourceService_UpdateResource
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1Resource'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: resource.name
          description: |-
            The name of the resource.
            Format: resources/{resource}, resource is the user defined if or uuid.
          in: path
          required: true
          type: string
          pattern: resources/[^/]+
        - name: resource
          in: body
          required: true
          schema:
            type: object
            properties:
              createTime:
                type: string
                format: date-time
                readOnly: true
              filename:
                type: string
              content:
                type: string
                format: byte
              externalLink:
                type: string
              type:
                type: string
              size:
                type: string
                format: int64
              memo:
                type: string
                description: The related memo. Refer to `Memo.name`.
      tags:
        - ResourceService
  /api/v1/{setting.name}:
    patch:
      summary: UpdateUserSetting updates the setting of a user.
      operationId: UserService_UpdateUserSetting
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiv1UserSetting'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: setting.name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+/setting
        - name: setting
          in: body
          required: true
          schema:
            type: object
            properties:
              locale:
                type: string
                description: The preferred locale of the user.
              appearance:
                type: string
                description: The preferred appearance of the user.
              memoVisibility:
                type: string
                description: The default visibility of the memo.
            required:
              - setting
      tags:
        - UserService
  /api/v1/{user.name}:
    patch:
      summary: UpdateUser updates a user.
      operationId: UserService_UpdateUser
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/v1User'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: user.name
          description: |-
            The name of the user.
            Format: users/{id}, id is the system generated auto-incremented id.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: user
          in: body
          required: true
          schema:
            type: object
            properties:
              role:
                $ref: '#/definitions/UserRole'
              username:
                type: string
              email:
                type: string
              nickname:
                type: string
              avatarUrl:
                type: string
              description:
                type: string
              password:
                type: string
              state:
                $ref: '#/definitions/v1State'
              createTime:
                type: string
                format: date-time
                readOnly: true
              updateTime:
                type: string
                format: date-time
                readOnly: true
            required:
              - user
      tags:
        - UserService
  /file/{name}/avatar:
    get:
      summary: GetUserAvatarBinary gets the avatar of a user.
      operationId: UserService_GetUserAvatarBinary
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiHttpBody'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the user.
          in: path
          required: true
          type: string
          pattern: users/[^/]+
        - name: httpBody.contentType
          description: The HTTP Content-Type header value specifying the content type of the body.
          in: query
          required: false
          type: string
        - name: httpBody.data
          description: The HTTP request/response body as raw binary.
          in: query
          required: false
          type: string
          format: byte
      tags:
        - UserService
  /file/{name}/{filename}:
    get:
      summary: GetResourceBinary returns a resource binary by name.
      operationId: ResourceService_GetResourceBinary
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/apiHttpBody'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/googlerpcStatus'
      parameters:
        - name: name
          description: The name of the resource.
          in: path
          required: true
          type: string
          pattern: resources/[^/]+
        - name: filename
          description: The filename of the resource. Mainly used for downloading.
          in: path
          required: true
          type: string
        - name: thumbnail
          description: A flag indicating if the thumbnail version of the resource should be returned
          in: query
          required: false
          type: boolean
      tags:
        - ResourceService
definitions:
  ListNodeKind:
    type: string
    enum:
      - KIND_UNSPECIFIED
      - ORDERED
      - UNORDERED
      - DESCRIPTION
    default: KIND_UNSPECIFIED
  MemoServiceRenameMemoTagBody:
    type: object
    properties:
      oldTag:
        type: string
      newTag:
        type: string
  MemoServiceSetMemoRelationsBody:
    type: object
    properties:
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
  MemoServiceSetMemoResourcesBody:
    type: object
    properties:
      resources:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Resource'
  MemoServiceUpsertMemoReactionBody:
    type: object
    properties:
      reaction:
        $ref: '#/definitions/v1Reaction'
  TableNodeRow:
    type: object
    properties:
      cells:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  UserRole:
    type: string
    enum:
      - ROLE_UNSPECIFIED
      - HOST
      - ADMIN
      - USER
    default: ROLE_UNSPECIFIED
  UserServiceCreateUserAccessTokenBody:
    type: object
    properties:
      description:
        type: string
      expiresAt:
        type: string
        format: date-time
  UserStatsMemoTypeStats:
    type: object
    properties:
      linkCount:
        type: integer
        format: int32
      codeCount:
        type: integer
        format: int32
      todoCount:
        type: integer
        format: int32
      undoCount:
        type: integer
        format: int32
  WorkspaceStorageSettingS3Config:
    type: object
    properties:
      accessKeyId:
        type: string
      accessKeySecret:
        type: string
      endpoint:
        type: string
      region:
        type: string
      bucket:
        type: string
      usePathStyle:
        type: boolean
    title: 'Reference: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-go/'
  apiHttpBody:
    type: object
    properties:
      contentType:
        type: string
        description: The HTTP Content-Type header value specifying the content type of the body.
      data:
        type: string
        format: byte
        description: The HTTP request/response body as raw binary.
      extensions:
        type: array
        items:
          type: object
          $ref: '#/definitions/protobufAny'
        description: |-
          Application specific response metadata. Must be set in the first response
          for streaming APIs.
    description: |-
      Message that represents an arbitrary HTTP body. It should only be used for
      payload formats that can't be represented as JSON, such as raw binary or
      an HTML page.


      This message can be used both in streaming and non-streaming API methods in
      the request as well as the response.

      It can be used as a top-level request field, which is convenient if one
      wants to extract parameters from either the URL or HTTP template into the
      request fields and also want access to the raw HTTP body.

      Example:

          message GetResourceRequest {
            // A unique request id.
            string request_id = 1;

            // The raw HTTP body is bound to this field.
            google.api.HttpBody http_body = 2;

          }

          service ResourceService {
            rpc GetResource(GetResourceRequest)
              returns (google.api.HttpBody);
            rpc UpdateResource(google.api.HttpBody)
              returns (google.protobuf.Empty);

          }

      Example with streaming methods:

          service CaldavService {
            rpc GetCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);
            rpc UpdateCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);

          }

      Use of this type only changes how the request and response bodies are
      handled, all other features will continue to work unchanged.
  apiv1ActivityMemoCommentPayload:
    type: object
    properties:
      memo:
        type: string
        description: |-
          The memo name of comment.
          Refer to `Memo.name`.
      relatedMemo:
        type: string
        description: The name of related memo.
    description: ActivityMemoCommentPayload represents the payload of a memo comment activity.
  apiv1ActivityPayload:
    type: object
    properties:
      memoComment:
        $ref: '#/definitions/apiv1ActivityMemoCommentPayload'
  apiv1FieldMapping:
    type: object
    properties:
      identifier:
        type: string
      displayName:
        type: string
      email:
        type: string
      avatarUrl:
        type: string
  apiv1IdentityProvider:
    type: object
    properties:
      name:
        type: string
        description: |-
          The name of the identityProvider.
          Format: identityProviders/{id}, id is the system generated auto-incremented id.
      type:
        $ref: '#/definitions/apiv1IdentityProviderType'
      title:
        type: string
      identifierFilter:
        type: string
      config:
        $ref: '#/definitions/apiv1IdentityProviderConfig'
  apiv1IdentityProviderConfig:
    type: object
    properties:
      oauth2Config:
        $ref: '#/definitions/apiv1OAuth2Config'
  apiv1IdentityProviderType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - OAUTH2
    default: TYPE_UNSPECIFIED
  apiv1Location:
    type: object
    properties:
      placeholder:
        type: string
      latitude:
        type: number
        format: double
      longitude:
        type: number
        format: double
  apiv1Memo:
    type: object
    properties:
      name:
        type: string
        description: |-
          The name of the memo.
          Format: memos/{memo}, memo is the user defined id or uuid.
        readOnly: true
      state:
        $ref: '#/definitions/v1State'
      creator:
        type: string
        title: |-
          The name of the creator.
          Format: users/{user}
      createTime:
        type: string
        format: date-time
      updateTime:
        type: string
        format: date-time
      displayTime:
        type: string
        format: date-time
      content:
        type: string
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
        readOnly: true
      visibility:
        $ref: '#/definitions/v1Visibility'
      tags:
        type: array
        items:
          type: string
        readOnly: true
      pinned:
        type: boolean
      resources:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Resource'
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
      reactions:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Reaction'
        readOnly: true
      property:
        $ref: '#/definitions/v1MemoProperty'
        readOnly: true
      parent:
        type: string
        title: |-
          The name of the parent memo.
          Format: memos/{id}
        readOnly: true
      snippet:
        type: string
        description: The snippet of the memo content. Plain text only.
        readOnly: true
      location:
        $ref: '#/definitions/apiv1Location'
        description: The location of the memo.
  apiv1OAuth2Config:
    type: object
    properties:
      clientId:
        type: string
      clientSecret:
        type: string
      authUrl:
        type: string
      tokenUrl:
        type: string
      userInfoUrl:
        type: string
      scopes:
        type: array
        items:
          type: string
      fieldMapping:
        $ref: '#/definitions/apiv1FieldMapping'
  apiv1Shortcut:
    type: object
    properties:
      id:
        type: string
      title:
        type: string
      filter:
        type: string
  apiv1UserSetting:
    type: object
    properties:
      name:
        type: string
        description: The name of the user.
      locale:
        type: string
        description: The preferred locale of the user.
      appearance:
        type: string
        description: The preferred appearance of the user.
      memoVisibility:
        type: string
        description: The default visibility of the memo.
  apiv1WorkspaceCustomProfile:
    type: object
    properties:
      title:
        type: string
      description:
        type: string
      logoUrl:
        type: string
      locale:
        type: string
      appearance:
        type: string
  apiv1WorkspaceGeneralSetting:
    type: object
    properties:
      disallowUserRegistration:
        type: boolean
        description: disallow_user_registration disallows user registration.
      disallowPasswordAuth:
        type: boolean
        description: disallow_password_auth disallows password authentication.
      additionalScript:
        type: string
        description: additional_script is the additional script.
      additionalStyle:
        type: string
        description: additional_style is the additional style.
      customProfile:
        $ref: '#/definitions/apiv1WorkspaceCustomProfile'
        description: custom_profile is the custom profile.
      weekStartDayOffset:
        type: integer
        format: int32
        description: |-
          week_start_day_offset is the week start day offset from Sunday.
          0: Sunday, 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday
          Default is Sunday.
      disallowChangeUsername:
        type: boolean
        description: disallow_change_username disallows changing username.
      disallowChangeNickname:
        type: boolean
        description: disallow_change_nickname disallows changing nickname.
  apiv1WorkspaceMemoRelatedSetting:
    type: object
    properties:
      disallowPublicVisibility:
        type: boolean
        description: disallow_public_visibility disallows set memo as public visibility.
      displayWithUpdateTime:
        type: boolean
        description: display_with_update_time orders and displays memo with update time.
      contentLengthLimit:
        type: integer
        format: int32
        description: content_length_limit is the limit of content length. Unit is byte.
      enableDoubleClickEdit:
        type: boolean
        description: enable_double_click_edit enables editing on double click.
      enableLinkPreview:
        type: boolean
        description: enable_link_preview enables links preview.
      enableComment:
        type: boolean
        description: enable_comment enables comment.
      reactions:
        type: array
        items:
          type: string
        description: reactions is the list of reactions.
      disableMarkdownShortcuts:
        type: boolean
        description: disable_markdown_shortcuts disallow the registration of markdown shortcuts.
      enableBlurNsfwContent:
        type: boolean
        description: enable_blur_nsfw_content enables blurring of content marked as not safe for work (NSFW).
      nsfwTags:
        type: array
        items:
          type: string
        description: nsfw_tags is the list of tags that mark content as NSFW for blurring.
  apiv1WorkspaceSetting:
    type: object
    properties:
      name:
        type: string
        title: |-
          name is the name of the setting.
          Format: settings/{setting}
      generalSetting:
        $ref: '#/definitions/apiv1WorkspaceGeneralSetting'
      storageSetting:
        $ref: '#/definitions/apiv1WorkspaceStorageSetting'
      memoRelatedSetting:
        $ref: '#/definitions/apiv1WorkspaceMemoRelatedSetting'
  apiv1WorkspaceStorageSetting:
    type: object
    properties:
      storageType:
        $ref: '#/definitions/apiv1WorkspaceStorageSettingStorageType'
        description: storage_type is the storage type.
      filepathTemplate:
        type: string
        title: |-
          The template of file path.
          e.g. assets/{timestamp}_{filename}
      uploadSizeLimitMb:
        type: string
        format: int64
        description: The max upload size in megabytes.
      s3Config:
        $ref: '#/definitions/WorkspaceStorageSettingS3Config'
        description: The S3 config.
  apiv1WorkspaceStorageSettingStorageType:
    type: string
    enum:
      - STORAGE_TYPE_UNSPECIFIED
      - DATABASE
      - LOCAL
      - S3
    default: STORAGE_TYPE_UNSPECIFIED
    description: |2-
       - DATABASE: DATABASE is the database storage type.
       - LOCAL: LOCAL is the local storage type.
       - S3: S3 is the S3 storage type.
  googlerpcStatus:
    type: object
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      details:
        type: array
        items:
          type: object
          $ref: '#/definitions/protobufAny'
  protobufAny:
    type: object
    properties:
      '@type':
        type: string
        description: |-
          A URL/resource name that uniquely identifies the type of the serialized
          protocol buffer message. This string must contain at least
          one "/" character. The last segment of the URL's path must represent
          the fully qualified name of the type (as in
          `path/google.protobuf.Duration`). The name should be in a canonical form
          (e.g., leading "." is not accepted).

          In practice, teams usually precompile into the binary all types that they
          expect it to use in the context of Any. However, for URLs which use the
          scheme `http`, `https`, or no scheme, one can optionally set up a type
          server that maps type URLs to message definitions as follows:

          * If no scheme is provided, `https` is assumed.
          * An HTTP GET on the URL must yield a [google.protobuf.Type][]
            value in binary format, or produce an error.
          * Applications are allowed to cache lookup results based on the
            URL, or have them precompiled into a binary to avoid any
            lookup. Therefore, binary compatibility needs to be preserved
            on changes to types. (Use versioned type names to manage
            breaking changes.)

          Note: this functionality is not currently available in the official
          protobuf release, and it is not used for type URLs beginning with
          type.googleapis.com. As of May 2023, there are no widely used type server
          implementations and no plans to implement one.

          Schemes other than `http`, `https` (or the empty scheme) might be
          used with implementation specific semantics.
    additionalProperties: {}
    description: |-
      `Any` contains an arbitrary serialized protocol buffer message along with a
      URL that describes the type of the serialized message.

      Protobuf library provides support to pack/unpack Any values in the form
      of utility functions or additional generated methods of the Any type.

      Example 1: Pack and unpack a message in C++.

          Foo foo = ...;
          Any any;
          any.PackFrom(foo);
          ...
          if (any.UnpackTo(&foo)) {
            ...
          }

      Example 2: Pack and unpack a message in Java.

          Foo foo = ...;
          Any any = Any.pack(foo);
          ...
          if (any.is(Foo.class)) {
            foo = any.unpack(Foo.class);
          }
          // or ...
          if (any.isSameTypeAs(Foo.getDefaultInstance())) {
            foo = any.unpack(Foo.getDefaultInstance());
          }

       Example 3: Pack and unpack a message in Python.

          foo = Foo(...)
          any = Any()
          any.Pack(foo)
          ...
          if any.Is(Foo.DESCRIPTOR):
            any.Unpack(foo)
            ...

       Example 4: Pack and unpack a message in Go

           foo := &pb.Foo{...}
           any, err := anypb.New(foo)
           if err != nil {
             ...
           }
           ...
           foo := &pb.Foo{}
           if err := any.UnmarshalTo(foo); err != nil {
             ...
           }

      The pack methods provided by protobuf library will by default use
      'type.googleapis.com/full.type.name' as the type URL and the unpack
      methods only use the fully qualified type name after the last '/'
      in the type URL, for example "foo.bar.com/x/y.z" will yield type
      name "y.z".

      JSON
      ====
      The JSON representation of an `Any` value uses the regular
      representation of the deserialized, embedded message, with an
      additional field `@type` which contains the type URL. Example:

          package google.profile;
          message Person {
            string first_name = 1;
            string last_name = 2;
          }

          {
            "@type": "type.googleapis.com/google.profile.Person",
            "firstName": <string>,
            "lastName": <string>
          }

      If the embedded message type is well-known and has a custom JSON
      representation, that representation will be embedded adding a field
      `value` which holds the custom JSON in addition to the `@type`
      field. Example (for message [google.protobuf.Duration][]):

          {
            "@type": "type.googleapis.com/google.protobuf.Duration",
            "value": "1.212s"
          }
  v1Activity:
    type: object
    properties:
      name:
        type: string
        title: |-
          The name of the activity.
          Format: activities/{id}
        readOnly: true
      creator:
        type: string
        title: |-
          The name of the creator.
          Format: users/{user}
      type:
        type: string
        description: The type of the activity.
      level:
        type: string
        description: The level of the activity.
      createTime:
        type: string
        format: date-time
        description: The create time of the activity.
        readOnly: true
      payload:
        $ref: '#/definitions/apiv1ActivityPayload'
        description: The payload of the activity.
  v1AutoLinkNode:
    type: object
    properties:
      url:
        type: string
      isRawText:
        type: boolean
  v1BlockquoteNode:
    type: object
    properties:
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1BoldItalicNode:
    type: object
    properties:
      symbol:
        type: string
      content:
        type: string
  v1BoldNode:
    type: object
    properties:
      symbol:
        type: string
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1CodeBlockNode:
    type: object
    properties:
      language:
        type: string
      content:
        type: string
  v1CodeNode:
    type: object
    properties:
      content:
        type: string
  v1CreateWebhookRequest:
    type: object
    properties:
      name:
        type: string
      url:
        type: string
  v1Direction:
    type: string
    enum:
      - DIRECTION_UNSPECIFIED
      - ASC
      - DESC
    default: DIRECTION_UNSPECIFIED
  v1EmbeddedContentNode:
    type: object
    properties:
      resourceName:
        type: string
      params:
        type: string
  v1EscapingCharacterNode:
    type: object
    properties:
      symbol:
        type: string
  v1HTMLElementNode:
    type: object
    properties:
      tagName:
        type: string
      attributes:
        type: object
        additionalProperties:
          type: string
  v1HeadingNode:
    type: object
    properties:
      level:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1HighlightNode:
    type: object
    properties:
      content:
        type: string
  v1HorizontalRuleNode:
    type: object
    properties:
      symbol:
        type: string
  v1ImageNode:
    type: object
    properties:
      altText:
        type: string
      url:
        type: string
  v1Inbox:
    type: object
    properties:
      name:
        type: string
        description: |-
          The name of the inbox.
          Format: inboxes/{id}, id is the system generated auto-incremented id.
      sender:
        type: string
        title: 'Format: users/{user}'
      receiver:
        type: string
        title: 'Format: users/{user}'
      status:
        $ref: '#/definitions/v1InboxStatus'
      createTime:
        type: string
        format: date-time
      type:
        $ref: '#/definitions/v1InboxType'
      activityId:
        type: integer
        format: int32
  v1InboxStatus:
    type: string
    enum:
      - STATUS_UNSPECIFIED
      - UNREAD
      - ARCHIVED
    default: STATUS_UNSPECIFIED
  v1InboxType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - MEMO_COMMENT
      - VERSION_UPDATE
    default: TYPE_UNSPECIFIED
  v1ItalicNode:
    type: object
    properties:
      symbol:
        type: string
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1LineBreakNode:
    type: object
  v1LinkMetadata:
    type: object
    properties:
      title:
        type: string
      description:
        type: string
      image:
        type: string
  v1LinkNode:
    type: object
    properties:
      content:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
      url:
        type: string
  v1ListAllUserStatsResponse:
    type: object
    properties:
      userStats:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1UserStats'
  v1ListIdentityProvidersResponse:
    type: object
    properties:
      identityProviders:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1IdentityProvider'
  v1ListInboxesResponse:
    type: object
    properties:
      inboxes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Inbox'
      nextPageToken:
        type: string
        description: |-
          A token, which can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
  v1ListMemoCommentsResponse:
    type: object
    properties:
      memos:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Memo'
  v1ListMemoReactionsResponse:
    type: object
    properties:
      reactions:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Reaction'
  v1ListMemoRelationsResponse:
    type: object
    properties:
      relations:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1MemoRelation'
  v1ListMemoResourcesResponse:
    type: object
    properties:
      resources:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Resource'
  v1ListMemosResponse:
    type: object
    properties:
      memos:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Memo'
      nextPageToken:
        type: string
        description: |-
          A token, which can be sent as `page_token` to retrieve the next page.
          If this field is omitted, there are no subsequent pages.
  v1ListNode:
    type: object
    properties:
      kind:
        $ref: '#/definitions/ListNodeKind'
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ListResourcesResponse:
    type: object
    properties:
      resources:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Resource'
  v1ListShortcutsResponse:
    type: object
    properties:
      shortcuts:
        type: array
        items:
          type: object
          $ref: '#/definitions/apiv1Shortcut'
  v1ListUserAccessTokensResponse:
    type: object
    properties:
      accessTokens:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1UserAccessToken'
  v1ListUsersResponse:
    type: object
    properties:
      users:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1User'
  v1ListWebhooksResponse:
    type: object
    properties:
      webhooks:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Webhook'
  v1MathBlockNode:
    type: object
    properties:
      content:
        type: string
  v1MathNode:
    type: object
    properties:
      content:
        type: string
  v1MemoProperty:
    type: object
    properties:
      hasLink:
        type: boolean
      hasTaskList:
        type: boolean
      hasCode:
        type: boolean
      hasIncompleteTasks:
        type: boolean
  v1MemoRelation:
    type: object
    properties:
      memo:
        $ref: '#/definitions/v1MemoRelationMemo'
      relatedMemo:
        $ref: '#/definitions/v1MemoRelationMemo'
      type:
        $ref: '#/definitions/v1MemoRelationType'
  v1MemoRelationMemo:
    type: object
    properties:
      name:
        type: string
        title: |-
          The name of the memo.
          Format: memos/{id}
      uid:
        type: string
      snippet:
        type: string
        description: The snippet of the memo content. Plain text only.
        readOnly: true
  v1MemoRelationType:
    type: string
    enum:
      - TYPE_UNSPECIFIED
      - REFERENCE
      - COMMENT
    default: TYPE_UNSPECIFIED
  v1Node:
    type: object
    properties:
      type:
        $ref: '#/definitions/v1NodeType'
      lineBreakNode:
        $ref: '#/definitions/v1LineBreakNode'
        description: Block nodes.
      paragraphNode:
        $ref: '#/definitions/v1ParagraphNode'
      codeBlockNode:
        $ref: '#/definitions/v1CodeBlockNode'
      headingNode:
        $ref: '#/definitions/v1HeadingNode'
      horizontalRuleNode:
        $ref: '#/definitions/v1HorizontalRuleNode'
      blockquoteNode:
        $ref: '#/definitions/v1BlockquoteNode'
      listNode:
        $ref: '#/definitions/v1ListNode'
      orderedListItemNode:
        $ref: '#/definitions/v1OrderedListItemNode'
      unorderedListItemNode:
        $ref: '#/definitions/v1UnorderedListItemNode'
      taskListItemNode:
        $ref: '#/definitions/v1TaskListItemNode'
      mathBlockNode:
        $ref: '#/definitions/v1MathBlockNode'
      tableNode:
        $ref: '#/definitions/v1TableNode'
      embeddedContentNode:
        $ref: '#/definitions/v1EmbeddedContentNode'
      textNode:
        $ref: '#/definitions/v1TextNode'
        description: Inline nodes.
      boldNode:
        $ref: '#/definitions/v1BoldNode'
      italicNode:
        $ref: '#/definitions/v1ItalicNode'
      boldItalicNode:
        $ref: '#/definitions/v1BoldItalicNode'
      codeNode:
        $ref: '#/definitions/v1CodeNode'
      imageNode:
        $ref: '#/definitions/v1ImageNode'
      linkNode:
        $ref: '#/definitions/v1LinkNode'
      autoLinkNode:
        $ref: '#/definitions/v1AutoLinkNode'
      tagNode:
        $ref: '#/definitions/v1TagNode'
      strikethroughNode:
        $ref: '#/definitions/v1StrikethroughNode'
      escapingCharacterNode:
        $ref: '#/definitions/v1EscapingCharacterNode'
      mathNode:
        $ref: '#/definitions/v1MathNode'
      highlightNode:
        $ref: '#/definitions/v1HighlightNode'
      subscriptNode:
        $ref: '#/definitions/v1SubscriptNode'
      superscriptNode:
        $ref: '#/definitions/v1SuperscriptNode'
      referencedContentNode:
        $ref: '#/definitions/v1ReferencedContentNode'
      spoilerNode:
        $ref: '#/definitions/v1SpoilerNode'
      htmlElementNode:
        $ref: '#/definitions/v1HTMLElementNode'
  v1NodeType:
    type: string
    enum:
      - NODE_UNSPECIFIED
      - LINE_BREAK
      - PARAGRAPH
      - CODE_BLOCK
      - HEADING
      - HORIZONTAL_RULE
      - BLOCKQUOTE
      - LIST
      - ORDERED_LIST_ITEM
      - UNORDERED_LIST_ITEM
      - TASK_LIST_ITEM
      - MATH_BLOCK
      - TABLE
      - EMBEDDED_CONTENT
      - TEXT
      - BOLD
      - ITALIC
      - BOLD_ITALIC
      - CODE
      - IMAGE
      - LINK
      - AUTO_LINK
      - TAG
      - STRIKETHROUGH
      - ESCAPING_CHARACTER
      - MATH
      - HIGHLIGHT
      - SUBSCRIPT
      - SUPERSCRIPT
      - REFERENCED_CONTENT
      - SPOILER
      - HTML_ELEMENT
    default: NODE_UNSPECIFIED
    description: |2-
       - LINE_BREAK: Block nodes.
       - TEXT: Inline nodes.
  v1OrderedListItemNode:
    type: object
    properties:
      number:
        type: string
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ParagraphNode:
    type: object
    properties:
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1ParseMarkdownRequest:
    type: object
    properties:
      markdown:
        type: string
  v1ParseMarkdownResponse:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1PasswordCredentials:
    type: object
    properties:
      username:
        type: string
        description: The username to sign in with.
      password:
        type: string
        description: The password to sign in with.
  v1Reaction:
    type: object
    properties:
      id:
        type: integer
        format: int32
      creator:
        type: string
        title: |-
          The name of the creator.
          Format: users/{user}
      contentId:
        type: string
        description: |-
          The content identifier.
          For memo, it should be the `Memo.name`.
      reactionType:
        type: string
  v1ReferencedContentNode:
    type: object
    properties:
      resourceName:
        type: string
      params:
        type: string
  v1Resource:
    type: object
    properties:
      name:
        type: string
        description: |-
          The name of the resource.
          Format: resources/{resource}, resource is the user defined if or uuid.
        readOnly: true
      createTime:
        type: string
        format: date-time
        readOnly: true
      filename:
        type: string
      content:
        type: string
        format: byte
      externalLink:
        type: string
      type:
        type: string
      size:
        type: string
        format: int64
      memo:
        type: string
        description: The related memo. Refer to `Memo.name`.
  v1RestoreMarkdownNodesRequest:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1RestoreMarkdownNodesResponse:
    type: object
    properties:
      markdown:
        type: string
  v1SSOCredentials:
    type: object
    properties:
      idpId:
        type: integer
        format: int32
        description: The ID of the SSO provider.
      code:
        type: string
        description: The code to sign in with.
      redirectUri:
        type: string
        description: The redirect URI.
  v1SpoilerNode:
    type: object
    properties:
      content:
        type: string
  v1State:
    type: string
    enum:
      - STATE_UNSPECIFIED
      - NORMAL
      - ARCHIVED
    default: STATE_UNSPECIFIED
  v1StrikethroughNode:
    type: object
    properties:
      content:
        type: string
  v1StringifyMarkdownNodesRequest:
    type: object
    properties:
      nodes:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1StringifyMarkdownNodesResponse:
    type: object
    properties:
      plainText:
        type: string
  v1SubscriptNode:
    type: object
    properties:
      content:
        type: string
  v1SuperscriptNode:
    type: object
    properties:
      content:
        type: string
  v1TableNode:
    type: object
    properties:
      header:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
      delimiter:
        type: array
        items:
          type: string
      rows:
        type: array
        items:
          type: object
          $ref: '#/definitions/TableNodeRow'
  v1TagNode:
    type: object
    properties:
      content:
        type: string
  v1TaskListItemNode:
    type: object
    properties:
      symbol:
        type: string
      indent:
        type: integer
        format: int32
      complete:
        type: boolean
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1TextNode:
    type: object
    properties:
      content:
        type: string
  v1UnorderedListItemNode:
    type: object
    properties:
      symbol:
        type: string
      indent:
        type: integer
        format: int32
      children:
        type: array
        items:
          type: object
          $ref: '#/definitions/v1Node'
  v1User:
    type: object
    properties:
      name:
        type: string
        description: |-
          The name of the user.
          Format: users/{id}, id is the system generated auto-incremented id.
        readOnly: true
      role:
        $ref: '#/definitions/UserRole'
      username:
        type: string
      email:
        type: string
      nickname:
        type: string
      avatarUrl:
        type: string
      description:
        type: string
      password:
        type: string
      state:
        $ref: '#/definitions/v1State'
      createTime:
        type: string
        format: date-time
        readOnly: true
      updateTime:
        type: string
        format: date-time
        readOnly: true
  v1UserAccessToken:
    type: object
    properties:
      accessToken:
        type: string
      description:
        type: string
      issuedAt:
        type: string
        format: date-time
      expiresAt:
        type: string
        format: date-time
  v1UserStats:
    type: object
    properties:
      name:
        type: string
        description: The name of the user.
      memoDisplayTimestamps:
        type: array
        items:
          type: string
          format: date-time
        description: |-
          The timestamps when the memos were displayed.
          We should return raw data to the client, and let the client format the data with the user's timezone.
      memoTypeStats:
        $ref: '#/definitions/UserStatsMemoTypeStats'
        description: The stats of memo types.
      tagCount:
        type: object
        additionalProperties:
          type: integer
          format: int32
        title: |-
          The count of tags.
          Format: "tag1": 1, "tag2": 2
      pinnedMemos:
        type: array
        items:
          type: string
        description: The pinned memos of the user.
      totalMemoCount:
        type: integer
        format: int32
  v1Visibility:
    type: string
    enum:
      - VISIBILITY_UNSPECIFIED
      - PRIVATE
      - PROTECTED
      - PUBLIC
    default: VISIBILITY_UNSPECIFIED
  v1Webhook:
    type: object
    properties:
      id:
        type: integer
        format: int32
      creator:
        type: string
        description: The name of the creator.
      createTime:
        type: string
        format: date-time
      updateTime:
        type: string
        format: date-time
      name:
        type: string
      url:
        type: string
  v1WorkspaceProfile:
    type: object
    properties:
      owner:
        type: string
        title: |-
          The name of instance owner.
          Format: users/{user}
      version:
        type: string
        title: version is the current version of instance
      mode:
        type: string
        description: mode is the instance mode (e.g. "prod", "dev" or "demo").
      instanceUrl:
        type: string
        description: instance_url is the URL of the instance.
