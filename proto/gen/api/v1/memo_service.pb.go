// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/memo_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Visibility int32

const (
	Visibility_VISIBILITY_UNSPECIFIED Visibility = 0
	Visibility_PRIVATE                Visibility = 1
	Visibility_PROTECTED              Visibility = 2
	Visibility_PUBLIC                 Visibility = 3
)

// Enum value maps for Visibility.
var (
	Visibility_name = map[int32]string{
		0: "VISIBILITY_UNSPECIFIED",
		1: "PRIVATE",
		2: "PROTECTED",
		3: "PUBLIC",
	}
	Visibility_value = map[string]int32{
		"VISIBILITY_UNSPECIFIED": 0,
		"PRIVATE":                1,
		"PROTECTED":              2,
		"PUBLIC":                 3,
	}
)

func (x Visibility) Enum() *Visibility {
	p := new(Visibility)
	*p = x
	return p
}

func (x Visibility) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Visibility) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_memo_service_proto_enumTypes[0].Descriptor()
}

func (Visibility) Type() protoreflect.EnumType {
	return &file_api_v1_memo_service_proto_enumTypes[0]
}

func (x Visibility) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Visibility.Descriptor instead.
func (Visibility) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{0}
}

type MemoRelation_Type int32

const (
	MemoRelation_TYPE_UNSPECIFIED MemoRelation_Type = 0
	MemoRelation_REFERENCE        MemoRelation_Type = 1
	MemoRelation_COMMENT          MemoRelation_Type = 2
)

// Enum value maps for MemoRelation_Type.
var (
	MemoRelation_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "REFERENCE",
		2: "COMMENT",
	}
	MemoRelation_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"REFERENCE":        1,
		"COMMENT":          2,
	}
)

func (x MemoRelation_Type) Enum() *MemoRelation_Type {
	p := new(MemoRelation_Type)
	*p = x
	return p
}

func (x MemoRelation_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MemoRelation_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_memo_service_proto_enumTypes[1].Descriptor()
}

func (MemoRelation_Type) Type() protoreflect.EnumType {
	return &file_api_v1_memo_service_proto_enumTypes[1]
}

func (x MemoRelation_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MemoRelation_Type.Descriptor instead.
func (MemoRelation_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{13, 0}
}

type Memo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	// Format: memos/{memo}, memo is the user defined id or uuid.
	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	State State  `protobuf:"varint,3,opt,name=state,proto3,enum=memos.api.v1.State" json:"state,omitempty"`
	// The name of the creator.
	// Format: users/{user}
	Creator     string                 `protobuf:"bytes,4,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	DisplayTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=display_time,json=displayTime,proto3" json:"display_time,omitempty"`
	Content     string                 `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`
	Nodes       []*Node                `protobuf:"bytes,9,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Visibility  Visibility             `protobuf:"varint,10,opt,name=visibility,proto3,enum=memos.api.v1.Visibility" json:"visibility,omitempty"`
	Tags        []string               `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	Pinned      bool                   `protobuf:"varint,12,opt,name=pinned,proto3" json:"pinned,omitempty"`
	Resources   []*Resource            `protobuf:"bytes,14,rep,name=resources,proto3" json:"resources,omitempty"`
	Relations   []*MemoRelation        `protobuf:"bytes,15,rep,name=relations,proto3" json:"relations,omitempty"`
	Reactions   []*Reaction            `protobuf:"bytes,16,rep,name=reactions,proto3" json:"reactions,omitempty"`
	Property    *Memo_Property         `protobuf:"bytes,17,opt,name=property,proto3" json:"property,omitempty"`
	// The name of the parent memo.
	// Format: memos/{id}
	Parent *string `protobuf:"bytes,18,opt,name=parent,proto3,oneof" json:"parent,omitempty"`
	// The snippet of the memo content. Plain text only.
	Snippet string `protobuf:"bytes,19,opt,name=snippet,proto3" json:"snippet,omitempty"`
	// The location of the memo.
	Location      *Location `protobuf:"bytes,20,opt,name=location,proto3,oneof" json:"location,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Memo) Reset() {
	*x = Memo{}
	mi := &file_api_v1_memo_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Memo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Memo) ProtoMessage() {}

func (x *Memo) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Memo.ProtoReflect.Descriptor instead.
func (*Memo) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{0}
}

func (x *Memo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Memo) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *Memo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Memo) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Memo) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Memo) GetDisplayTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DisplayTime
	}
	return nil
}

func (x *Memo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Memo) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Memo) GetVisibility() Visibility {
	if x != nil {
		return x.Visibility
	}
	return Visibility_VISIBILITY_UNSPECIFIED
}

func (x *Memo) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Memo) GetPinned() bool {
	if x != nil {
		return x.Pinned
	}
	return false
}

func (x *Memo) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Memo) GetRelations() []*MemoRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

func (x *Memo) GetReactions() []*Reaction {
	if x != nil {
		return x.Reactions
	}
	return nil
}

func (x *Memo) GetProperty() *Memo_Property {
	if x != nil {
		return x.Property
	}
	return nil
}

func (x *Memo) GetParent() string {
	if x != nil && x.Parent != nil {
		return *x.Parent
	}
	return ""
}

func (x *Memo) GetSnippet() string {
	if x != nil {
		return x.Snippet
	}
	return ""
}

func (x *Memo) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

type Location struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Placeholder   string                 `protobuf:"bytes,1,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	Latitude      float64                `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude     float64                `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Location) Reset() {
	*x = Location{}
	mi := &file_api_v1_memo_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{1}
}

func (x *Location) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

func (x *Location) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Location) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

type CreateMemoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The memo to create.
	Memo          *Memo `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMemoRequest) Reset() {
	*x = CreateMemoRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMemoRequest) ProtoMessage() {}

func (x *CreateMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMemoRequest.ProtoReflect.Descriptor instead.
func (*CreateMemoRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMemoRequest) GetMemo() *Memo {
	if x != nil {
		return x.Memo
	}
	return nil
}

type ListMemosRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The parent is the owner of the memos.
	// If not specified or `users/-`, it will list all memos.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// The maximum number of memos to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A page token, received from a previous `ListMemos` call.
	// Provide this to retrieve the subsequent page.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// The state of the memos to list.
	// Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
	State State `protobuf:"varint,4,opt,name=state,proto3,enum=memos.api.v1.State" json:"state,omitempty"`
	// What field to sort the results by.
	// Default to display_time.
	Sort string `protobuf:"bytes,5,opt,name=sort,proto3" json:"sort,omitempty"`
	// The direction to sort the results by.
	// Default to DESC.
	Direction Direction `protobuf:"varint,6,opt,name=direction,proto3,enum=memos.api.v1.Direction" json:"direction,omitempty"`
	// Filter is a CEL expression to filter memos.
	// Refer to `Shortcut.filter`.
	Filter string `protobuf:"bytes,7,opt,name=filter,proto3" json:"filter,omitempty"`
	// [Deprecated] Old filter contains some specific conditions to filter memos.
	// Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
	OldFilter     string `protobuf:"bytes,8,opt,name=old_filter,json=oldFilter,proto3" json:"old_filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemosRequest) Reset() {
	*x = ListMemosRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemosRequest) ProtoMessage() {}

func (x *ListMemosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemosRequest.ProtoReflect.Descriptor instead.
func (*ListMemosRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListMemosRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListMemosRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListMemosRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListMemosRequest) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *ListMemosRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *ListMemosRequest) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

func (x *ListMemosRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListMemosRequest) GetOldFilter() string {
	if x != nil {
		return x.OldFilter
	}
	return ""
}

type ListMemosResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Memos []*Memo                `protobuf:"bytes,1,rep,name=memos,proto3" json:"memos,omitempty"`
	// A token, which can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemosResponse) Reset() {
	*x = ListMemosResponse{}
	mi := &file_api_v1_memo_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemosResponse) ProtoMessage() {}

func (x *ListMemosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemosResponse.ProtoReflect.Descriptor instead.
func (*ListMemosResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListMemosResponse) GetMemos() []*Memo {
	if x != nil {
		return x.Memos
	}
	return nil
}

func (x *ListMemosResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type GetMemoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemoRequest) Reset() {
	*x = GetMemoRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemoRequest) ProtoMessage() {}

func (x *GetMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemoRequest.ProtoReflect.Descriptor instead.
func (*GetMemoRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetMemoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UpdateMemoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The memo to update.
	// The `name` field is required.
	Memo          *Memo                  `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMemoRequest) Reset() {
	*x = UpdateMemoRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemoRequest) ProtoMessage() {}

func (x *UpdateMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemoRequest.ProtoReflect.Descriptor instead.
func (*UpdateMemoRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateMemoRequest) GetMemo() *Memo {
	if x != nil {
		return x.Memo
	}
	return nil
}

func (x *UpdateMemoRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteMemoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMemoRequest) Reset() {
	*x = DeleteMemoRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMemoRequest) ProtoMessage() {}

func (x *DeleteMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMemoRequest.ProtoReflect.Descriptor instead.
func (*DeleteMemoRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteMemoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RenameMemoTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The parent, who owns the tags.
	// Format: memos/{id}. Use "memos/-" to rename all tags.
	Parent        string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	OldTag        string `protobuf:"bytes,2,opt,name=old_tag,json=oldTag,proto3" json:"old_tag,omitempty"`
	NewTag        string `protobuf:"bytes,3,opt,name=new_tag,json=newTag,proto3" json:"new_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RenameMemoTagRequest) Reset() {
	*x = RenameMemoTagRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenameMemoTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameMemoTagRequest) ProtoMessage() {}

func (x *RenameMemoTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameMemoTagRequest.ProtoReflect.Descriptor instead.
func (*RenameMemoTagRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{8}
}

func (x *RenameMemoTagRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *RenameMemoTagRequest) GetOldTag() string {
	if x != nil {
		return x.OldTag
	}
	return ""
}

func (x *RenameMemoTagRequest) GetNewTag() string {
	if x != nil {
		return x.NewTag
	}
	return ""
}

type DeleteMemoTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The parent, who owns the tags.
	// Format: memos/{id}. Use "memos/-" to delete all tags.
	Parent             string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	Tag                string `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	DeleteRelatedMemos bool   `protobuf:"varint,3,opt,name=delete_related_memos,json=deleteRelatedMemos,proto3" json:"delete_related_memos,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeleteMemoTagRequest) Reset() {
	*x = DeleteMemoTagRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMemoTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMemoTagRequest) ProtoMessage() {}

func (x *DeleteMemoTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMemoTagRequest.ProtoReflect.Descriptor instead.
func (*DeleteMemoTagRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteMemoTagRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *DeleteMemoTagRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *DeleteMemoTagRequest) GetDeleteRelatedMemos() bool {
	if x != nil {
		return x.DeleteRelatedMemos
	}
	return false
}

type SetMemoResourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Resources     []*Resource `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMemoResourcesRequest) Reset() {
	*x = SetMemoResourcesRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMemoResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMemoResourcesRequest) ProtoMessage() {}

func (x *SetMemoResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMemoResourcesRequest.ProtoReflect.Descriptor instead.
func (*SetMemoResourcesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{10}
}

func (x *SetMemoResourcesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetMemoResourcesRequest) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

type ListMemoResourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoResourcesRequest) Reset() {
	*x = ListMemoResourcesRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoResourcesRequest) ProtoMessage() {}

func (x *ListMemoResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoResourcesRequest.ProtoReflect.Descriptor instead.
func (*ListMemoResourcesRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListMemoResourcesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListMemoResourcesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resources     []*Resource            `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoResourcesResponse) Reset() {
	*x = ListMemoResourcesResponse{}
	mi := &file_api_v1_memo_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoResourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoResourcesResponse) ProtoMessage() {}

func (x *ListMemoResourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoResourcesResponse.ProtoReflect.Descriptor instead.
func (*ListMemoResourcesResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListMemoResourcesResponse) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

type MemoRelation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Memo          *MemoRelation_Memo     `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
	RelatedMemo   *MemoRelation_Memo     `protobuf:"bytes,2,opt,name=related_memo,json=relatedMemo,proto3" json:"related_memo,omitempty"`
	Type          MemoRelation_Type      `protobuf:"varint,3,opt,name=type,proto3,enum=memos.api.v1.MemoRelation_Type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoRelation) Reset() {
	*x = MemoRelation{}
	mi := &file_api_v1_memo_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoRelation) ProtoMessage() {}

func (x *MemoRelation) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoRelation.ProtoReflect.Descriptor instead.
func (*MemoRelation) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{13}
}

func (x *MemoRelation) GetMemo() *MemoRelation_Memo {
	if x != nil {
		return x.Memo
	}
	return nil
}

func (x *MemoRelation) GetRelatedMemo() *MemoRelation_Memo {
	if x != nil {
		return x.RelatedMemo
	}
	return nil
}

func (x *MemoRelation) GetType() MemoRelation_Type {
	if x != nil {
		return x.Type
	}
	return MemoRelation_TYPE_UNSPECIFIED
}

type SetMemoRelationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Relations     []*MemoRelation `protobuf:"bytes,2,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMemoRelationsRequest) Reset() {
	*x = SetMemoRelationsRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMemoRelationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMemoRelationsRequest) ProtoMessage() {}

func (x *SetMemoRelationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMemoRelationsRequest.ProtoReflect.Descriptor instead.
func (*SetMemoRelationsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{14}
}

func (x *SetMemoRelationsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetMemoRelationsRequest) GetRelations() []*MemoRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type ListMemoRelationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoRelationsRequest) Reset() {
	*x = ListMemoRelationsRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoRelationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoRelationsRequest) ProtoMessage() {}

func (x *ListMemoRelationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoRelationsRequest.ProtoReflect.Descriptor instead.
func (*ListMemoRelationsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListMemoRelationsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListMemoRelationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*MemoRelation        `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoRelationsResponse) Reset() {
	*x = ListMemoRelationsResponse{}
	mi := &file_api_v1_memo_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoRelationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoRelationsResponse) ProtoMessage() {}

func (x *ListMemoRelationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoRelationsResponse.ProtoReflect.Descriptor instead.
func (*ListMemoRelationsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListMemoRelationsResponse) GetRelations() []*MemoRelation {
	if x != nil {
		return x.Relations
	}
	return nil
}

type CreateMemoCommentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The comment to create.
	Comment       *Memo `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMemoCommentRequest) Reset() {
	*x = CreateMemoCommentRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMemoCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMemoCommentRequest) ProtoMessage() {}

func (x *CreateMemoCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMemoCommentRequest.ProtoReflect.Descriptor instead.
func (*CreateMemoCommentRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{17}
}

func (x *CreateMemoCommentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMemoCommentRequest) GetComment() *Memo {
	if x != nil {
		return x.Comment
	}
	return nil
}

type ListMemoCommentsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoCommentsRequest) Reset() {
	*x = ListMemoCommentsRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoCommentsRequest) ProtoMessage() {}

func (x *ListMemoCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoCommentsRequest.ProtoReflect.Descriptor instead.
func (*ListMemoCommentsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{18}
}

func (x *ListMemoCommentsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListMemoCommentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Memos         []*Memo                `protobuf:"bytes,1,rep,name=memos,proto3" json:"memos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoCommentsResponse) Reset() {
	*x = ListMemoCommentsResponse{}
	mi := &file_api_v1_memo_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoCommentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoCommentsResponse) ProtoMessage() {}

func (x *ListMemoCommentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoCommentsResponse.ProtoReflect.Descriptor instead.
func (*ListMemoCommentsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{19}
}

func (x *ListMemoCommentsResponse) GetMemos() []*Memo {
	if x != nil {
		return x.Memos
	}
	return nil
}

type ListMemoReactionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoReactionsRequest) Reset() {
	*x = ListMemoReactionsRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoReactionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoReactionsRequest) ProtoMessage() {}

func (x *ListMemoReactionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoReactionsRequest.ProtoReflect.Descriptor instead.
func (*ListMemoReactionsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListMemoReactionsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListMemoReactionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Reactions     []*Reaction            `protobuf:"bytes,1,rep,name=reactions,proto3" json:"reactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMemoReactionsResponse) Reset() {
	*x = ListMemoReactionsResponse{}
	mi := &file_api_v1_memo_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoReactionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoReactionsResponse) ProtoMessage() {}

func (x *ListMemoReactionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoReactionsResponse.ProtoReflect.Descriptor instead.
func (*ListMemoReactionsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListMemoReactionsResponse) GetReactions() []*Reaction {
	if x != nil {
		return x.Reactions
	}
	return nil
}

type UpsertMemoReactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	Name          string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Reaction      *Reaction `protobuf:"bytes,2,opt,name=reaction,proto3" json:"reaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpsertMemoReactionRequest) Reset() {
	*x = UpsertMemoReactionRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpsertMemoReactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertMemoReactionRequest) ProtoMessage() {}

func (x *UpsertMemoReactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertMemoReactionRequest.ProtoReflect.Descriptor instead.
func (*UpsertMemoReactionRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpsertMemoReactionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpsertMemoReactionRequest) GetReaction() *Reaction {
	if x != nil {
		return x.Reaction
	}
	return nil
}

type DeleteMemoReactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The id of the reaction.
	// Refer to the `Reaction.id`.
	Id            int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMemoReactionRequest) Reset() {
	*x = DeleteMemoReactionRequest{}
	mi := &file_api_v1_memo_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMemoReactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMemoReactionRequest) ProtoMessage() {}

func (x *DeleteMemoReactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMemoReactionRequest.ProtoReflect.Descriptor instead.
func (*DeleteMemoReactionRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteMemoReactionRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Memo_Property struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	HasLink            bool                   `protobuf:"varint,1,opt,name=has_link,json=hasLink,proto3" json:"has_link,omitempty"`
	HasTaskList        bool                   `protobuf:"varint,2,opt,name=has_task_list,json=hasTaskList,proto3" json:"has_task_list,omitempty"`
	HasCode            bool                   `protobuf:"varint,3,opt,name=has_code,json=hasCode,proto3" json:"has_code,omitempty"`
	HasIncompleteTasks bool                   `protobuf:"varint,4,opt,name=has_incomplete_tasks,json=hasIncompleteTasks,proto3" json:"has_incomplete_tasks,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Memo_Property) Reset() {
	*x = Memo_Property{}
	mi := &file_api_v1_memo_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Memo_Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Memo_Property) ProtoMessage() {}

func (x *Memo_Property) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Memo_Property.ProtoReflect.Descriptor instead.
func (*Memo_Property) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Memo_Property) GetHasLink() bool {
	if x != nil {
		return x.HasLink
	}
	return false
}

func (x *Memo_Property) GetHasTaskList() bool {
	if x != nil {
		return x.HasTaskList
	}
	return false
}

func (x *Memo_Property) GetHasCode() bool {
	if x != nil {
		return x.HasCode
	}
	return false
}

func (x *Memo_Property) GetHasIncompleteTasks() bool {
	if x != nil {
		return x.HasIncompleteTasks
	}
	return false
}

type MemoRelation_Memo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the memo.
	// Format: memos/{id}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Uid  string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// The snippet of the memo content. Plain text only.
	Snippet       string `protobuf:"bytes,3,opt,name=snippet,proto3" json:"snippet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemoRelation_Memo) Reset() {
	*x = MemoRelation_Memo{}
	mi := &file_api_v1_memo_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoRelation_Memo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoRelation_Memo) ProtoMessage() {}

func (x *MemoRelation_Memo) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_memo_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoRelation_Memo.ProtoReflect.Descriptor instead.
func (*MemoRelation_Memo) Descriptor() ([]byte, []int) {
	return file_api_v1_memo_service_proto_rawDescGZIP(), []int{13, 0}
}

func (x *MemoRelation_Memo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MemoRelation_Memo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *MemoRelation_Memo) GetSnippet() string {
	if x != nil {
		return x.Snippet
	}
	return ""
}

var File_api_v1_memo_service_proto protoreflect.FileDescriptor

const file_api_v1_memo_service_proto_rawDesc = "" +
	"\n" +
	"\x19api/v1/memo_service.proto\x12\fmemos.api.v1\x1a\x13api/v1/common.proto\x1a\x1dapi/v1/markdown_service.proto\x1a\x1dapi/v1/reaction_service.proto\x1a\x1dapi/v1/resource_service.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xee\a\n" +
	"\x04Memo\x12\x1a\n" +
	"\x04name\x18\x01 \x01(\tB\x06\xe0A\x03\xe0A\bR\x04name\x12)\n" +
	"\x05state\x18\x03 \x01(\x0e2\x13.memos.api.v1.StateR\x05state\x12\x18\n" +
	"\acreator\x18\x04 \x01(\tR\acreator\x12;\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12=\n" +
	"\fdisplay_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vdisplayTime\x12\x18\n" +
	"\acontent\x18\b \x01(\tR\acontent\x12-\n" +
	"\x05nodes\x18\t \x03(\v2\x12.memos.api.v1.NodeB\x03\xe0A\x03R\x05nodes\x128\n" +
	"\n" +
	"visibility\x18\n" +
	" \x01(\x0e2\x18.memos.api.v1.VisibilityR\n" +
	"visibility\x12\x17\n" +
	"\x04tags\x18\v \x03(\tB\x03\xe0A\x03R\x04tags\x12\x16\n" +
	"\x06pinned\x18\f \x01(\bR\x06pinned\x124\n" +
	"\tresources\x18\x0e \x03(\v2\x16.memos.api.v1.ResourceR\tresources\x128\n" +
	"\trelations\x18\x0f \x03(\v2\x1a.memos.api.v1.MemoRelationR\trelations\x129\n" +
	"\treactions\x18\x10 \x03(\v2\x16.memos.api.v1.ReactionB\x03\xe0A\x03R\treactions\x12<\n" +
	"\bproperty\x18\x11 \x01(\v2\x1b.memos.api.v1.Memo.PropertyB\x03\xe0A\x03R\bproperty\x12 \n" +
	"\x06parent\x18\x12 \x01(\tB\x03\xe0A\x03H\x00R\x06parent\x88\x01\x01\x12\x1d\n" +
	"\asnippet\x18\x13 \x01(\tB\x03\xe0A\x03R\asnippet\x127\n" +
	"\blocation\x18\x14 \x01(\v2\x16.memos.api.v1.LocationH\x01R\blocation\x88\x01\x01\x1a\x96\x01\n" +
	"\bProperty\x12\x19\n" +
	"\bhas_link\x18\x01 \x01(\bR\ahasLink\x12\"\n" +
	"\rhas_task_list\x18\x02 \x01(\bR\vhasTaskList\x12\x19\n" +
	"\bhas_code\x18\x03 \x01(\bR\ahasCode\x120\n" +
	"\x14has_incomplete_tasks\x18\x04 \x01(\bR\x12hasIncompleteTasksB\t\n" +
	"\a_parentB\v\n" +
	"\t_locationJ\x04\b\x02\x10\x03\"f\n" +
	"\bLocation\x12 \n" +
	"\vplaceholder\x18\x01 \x01(\tR\vplaceholder\x12\x1a\n" +
	"\blatitude\x18\x02 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x03 \x01(\x01R\tlongitude\"@\n" +
	"\x11CreateMemoRequest\x12+\n" +
	"\x04memo\x18\x01 \x01(\v2\x12.memos.api.v1.MemoB\x03\xe0A\x02R\x04memo\"\x93\x02\n" +
	"\x10ListMemosRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tR\tpageToken\x12)\n" +
	"\x05state\x18\x04 \x01(\x0e2\x13.memos.api.v1.StateR\x05state\x12\x12\n" +
	"\x04sort\x18\x05 \x01(\tR\x04sort\x125\n" +
	"\tdirection\x18\x06 \x01(\x0e2\x17.memos.api.v1.DirectionR\tdirection\x12\x16\n" +
	"\x06filter\x18\a \x01(\tR\x06filter\x12\x1d\n" +
	"\n" +
	"old_filter\x18\b \x01(\tR\toldFilter\"e\n" +
	"\x11ListMemosResponse\x12(\n" +
	"\x05memos\x18\x01 \x03(\v2\x12.memos.api.v1.MemoR\x05memos\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"$\n" +
	"\x0eGetMemoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"}\n" +
	"\x11UpdateMemoRequest\x12+\n" +
	"\x04memo\x18\x01 \x01(\v2\x12.memos.api.v1.MemoB\x03\xe0A\x02R\x04memo\x12;\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"'\n" +
	"\x11DeleteMemoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"`\n" +
	"\x14RenameMemoTagRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x12\x17\n" +
	"\aold_tag\x18\x02 \x01(\tR\x06oldTag\x12\x17\n" +
	"\anew_tag\x18\x03 \x01(\tR\x06newTag\"r\n" +
	"\x14DeleteMemoTagRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x12\x10\n" +
	"\x03tag\x18\x02 \x01(\tR\x03tag\x120\n" +
	"\x14delete_related_memos\x18\x03 \x01(\bR\x12deleteRelatedMemos\"c\n" +
	"\x17SetMemoResourcesRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x124\n" +
	"\tresources\x18\x02 \x03(\v2\x16.memos.api.v1.ResourceR\tresources\".\n" +
	"\x18ListMemoResourcesRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"Q\n" +
	"\x19ListMemoResourcesResponse\x124\n" +
	"\tresources\x18\x01 \x03(\v2\x16.memos.api.v1.ResourceR\tresources\"\xc3\x02\n" +
	"\fMemoRelation\x123\n" +
	"\x04memo\x18\x01 \x01(\v2\x1f.memos.api.v1.MemoRelation.MemoR\x04memo\x12B\n" +
	"\frelated_memo\x18\x02 \x01(\v2\x1f.memos.api.v1.MemoRelation.MemoR\vrelatedMemo\x123\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1f.memos.api.v1.MemoRelation.TypeR\x04type\x1aK\n" +
	"\x04Memo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\tR\x03uid\x12\x1d\n" +
	"\asnippet\x18\x03 \x01(\tB\x03\xe0A\x03R\asnippet\"8\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tREFERENCE\x10\x01\x12\v\n" +
	"\aCOMMENT\x10\x02\"g\n" +
	"\x17SetMemoRelationsRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x128\n" +
	"\trelations\x18\x02 \x03(\v2\x1a.memos.api.v1.MemoRelationR\trelations\".\n" +
	"\x18ListMemoRelationsRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"U\n" +
	"\x19ListMemoRelationsResponse\x128\n" +
	"\trelations\x18\x01 \x03(\v2\x1a.memos.api.v1.MemoRelationR\trelations\"\\\n" +
	"\x18CreateMemoCommentRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12,\n" +
	"\acomment\x18\x02 \x01(\v2\x12.memos.api.v1.MemoR\acomment\"-\n" +
	"\x17ListMemoCommentsRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"D\n" +
	"\x18ListMemoCommentsResponse\x12(\n" +
	"\x05memos\x18\x01 \x03(\v2\x12.memos.api.v1.MemoR\x05memos\".\n" +
	"\x18ListMemoReactionsRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"Q\n" +
	"\x19ListMemoReactionsResponse\x124\n" +
	"\treactions\x18\x01 \x03(\v2\x16.memos.api.v1.ReactionR\treactions\"c\n" +
	"\x19UpsertMemoReactionRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x122\n" +
	"\breaction\x18\x02 \x01(\v2\x16.memos.api.v1.ReactionR\breaction\"+\n" +
	"\x19DeleteMemoReactionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id*P\n" +
	"\n" +
	"Visibility\x12\x1a\n" +
	"\x16VISIBILITY_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aPRIVATE\x10\x01\x12\r\n" +
	"\tPROTECTED\x10\x02\x12\n" +
	"\n" +
	"\x06PUBLIC\x10\x032\xbf\x10\n" +
	"\vMemoService\x12^\n" +
	"\n" +
	"CreateMemo\x12\x1f.memos.api.v1.CreateMemoRequest\x1a\x12.memos.api.v1.Memo\"\x1b\x82\xd3\xe4\x93\x02\x15:\x04memo\"\r/api/v1/memos\x12\x85\x01\n" +
	"\tListMemos\x12\x1e.memos.api.v1.ListMemosRequest\x1a\x1f.memos.api.v1.ListMemosResponse\"7\x82\xd3\xe4\x93\x021Z \x12\x1e/api/v1/{parent=users/*}/memos\x12\r/api/v1/memos\x12b\n" +
	"\aGetMemo\x12\x1c.memos.api.v1.GetMemoRequest\x1a\x12.memos.api.v1.Memo\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/{name=memos/*}\x12\x7f\n" +
	"\n" +
	"UpdateMemo\x12\x1f.memos.api.v1.UpdateMemoRequest\x1a\x12.memos.api.v1.Memo\"<\xdaA\x10memo,update_mask\x82\xd3\xe4\x93\x02#:\x04memo2\x1b/api/v1/{memo.name=memos/*}\x12l\n" +
	"\n" +
	"DeleteMemo\x12\x1f.memos.api.v1.DeleteMemoRequest\x1a\x16.google.protobuf.Empty\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18*\x16/api/v1/{name=memos/*}\x12|\n" +
	"\rRenameMemoTag\x12\".memos.api.v1.RenameMemoTagRequest\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*2$/api/v1/{parent=memos/*}/tags:rename\x12x\n" +
	"\rDeleteMemoTag\x12\".memos.api.v1.DeleteMemoTagRequest\x1a\x16.google.protobuf.Empty\"+\x82\xd3\xe4\x93\x02%*#/api/v1/{parent=memos/*}/tags/{tag}\x12\x85\x01\n" +
	"\x10SetMemoResources\x12%.memos.api.v1.SetMemoResourcesRequest\x1a\x16.google.protobuf.Empty\"2\xdaA\x04name\x82\xd3\xe4\x93\x02%:\x01*2 /api/v1/{name=memos/*}/resources\x12\x95\x01\n" +
	"\x11ListMemoResources\x12&.memos.api.v1.ListMemoResourcesRequest\x1a'.memos.api.v1.ListMemoResourcesResponse\"/\xdaA\x04name\x82\xd3\xe4\x93\x02\"\x12 /api/v1/{name=memos/*}/resources\x12\x85\x01\n" +
	"\x10SetMemoRelations\x12%.memos.api.v1.SetMemoRelationsRequest\x1a\x16.google.protobuf.Empty\"2\xdaA\x04name\x82\xd3\xe4\x93\x02%:\x01*2 /api/v1/{name=memos/*}/relations\x12\x95\x01\n" +
	"\x11ListMemoRelations\x12&.memos.api.v1.ListMemoRelationsRequest\x1a'.memos.api.v1.ListMemoRelationsResponse\"/\xdaA\x04name\x82\xd3\xe4\x93\x02\"\x12 /api/v1/{name=memos/*}/relations\x12\x88\x01\n" +
	"\x11CreateMemoComment\x12&.memos.api.v1.CreateMemoCommentRequest\x1a\x12.memos.api.v1.Memo\"7\xdaA\x04name\x82\xd3\xe4\x93\x02*:\acomment\"\x1f/api/v1/{name=memos/*}/comments\x12\x91\x01\n" +
	"\x10ListMemoComments\x12%.memos.api.v1.ListMemoCommentsRequest\x1a&.memos.api.v1.ListMemoCommentsResponse\".\xdaA\x04name\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/{name=memos/*}/comments\x12\x95\x01\n" +
	"\x11ListMemoReactions\x12&.memos.api.v1.ListMemoReactionsRequest\x1a'.memos.api.v1.ListMemoReactionsResponse\"/\xdaA\x04name\x82\xd3\xe4\x93\x02\"\x12 /api/v1/{name=memos/*}/reactions\x12\x89\x01\n" +
	"\x12UpsertMemoReaction\x12'.memos.api.v1.UpsertMemoReactionRequest\x1a\x16.memos.api.v1.Reaction\"2\xdaA\x04name\x82\xd3\xe4\x93\x02%:\x01*\" /api/v1/{name=memos/*}/reactions\x12z\n" +
	"\x12DeleteMemoReaction\x12'.memos.api.v1.DeleteMemoReactionRequest\x1a\x16.google.protobuf.Empty\"#\xdaA\x02id\x82\xd3\xe4\x93\x02\x18*\x16/api/v1/reactions/{id}B\xa8\x01\n" +
	"\x10com.memos.api.v1B\x10MemoServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_memo_service_proto_rawDescOnce sync.Once
	file_api_v1_memo_service_proto_rawDescData []byte
)

func file_api_v1_memo_service_proto_rawDescGZIP() []byte {
	file_api_v1_memo_service_proto_rawDescOnce.Do(func() {
		file_api_v1_memo_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_memo_service_proto_rawDesc), len(file_api_v1_memo_service_proto_rawDesc)))
	})
	return file_api_v1_memo_service_proto_rawDescData
}

var file_api_v1_memo_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_v1_memo_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_api_v1_memo_service_proto_goTypes = []any{
	(Visibility)(0),                   // 0: memos.api.v1.Visibility
	(MemoRelation_Type)(0),            // 1: memos.api.v1.MemoRelation.Type
	(*Memo)(nil),                      // 2: memos.api.v1.Memo
	(*Location)(nil),                  // 3: memos.api.v1.Location
	(*CreateMemoRequest)(nil),         // 4: memos.api.v1.CreateMemoRequest
	(*ListMemosRequest)(nil),          // 5: memos.api.v1.ListMemosRequest
	(*ListMemosResponse)(nil),         // 6: memos.api.v1.ListMemosResponse
	(*GetMemoRequest)(nil),            // 7: memos.api.v1.GetMemoRequest
	(*UpdateMemoRequest)(nil),         // 8: memos.api.v1.UpdateMemoRequest
	(*DeleteMemoRequest)(nil),         // 9: memos.api.v1.DeleteMemoRequest
	(*RenameMemoTagRequest)(nil),      // 10: memos.api.v1.RenameMemoTagRequest
	(*DeleteMemoTagRequest)(nil),      // 11: memos.api.v1.DeleteMemoTagRequest
	(*SetMemoResourcesRequest)(nil),   // 12: memos.api.v1.SetMemoResourcesRequest
	(*ListMemoResourcesRequest)(nil),  // 13: memos.api.v1.ListMemoResourcesRequest
	(*ListMemoResourcesResponse)(nil), // 14: memos.api.v1.ListMemoResourcesResponse
	(*MemoRelation)(nil),              // 15: memos.api.v1.MemoRelation
	(*SetMemoRelationsRequest)(nil),   // 16: memos.api.v1.SetMemoRelationsRequest
	(*ListMemoRelationsRequest)(nil),  // 17: memos.api.v1.ListMemoRelationsRequest
	(*ListMemoRelationsResponse)(nil), // 18: memos.api.v1.ListMemoRelationsResponse
	(*CreateMemoCommentRequest)(nil),  // 19: memos.api.v1.CreateMemoCommentRequest
	(*ListMemoCommentsRequest)(nil),   // 20: memos.api.v1.ListMemoCommentsRequest
	(*ListMemoCommentsResponse)(nil),  // 21: memos.api.v1.ListMemoCommentsResponse
	(*ListMemoReactionsRequest)(nil),  // 22: memos.api.v1.ListMemoReactionsRequest
	(*ListMemoReactionsResponse)(nil), // 23: memos.api.v1.ListMemoReactionsResponse
	(*UpsertMemoReactionRequest)(nil), // 24: memos.api.v1.UpsertMemoReactionRequest
	(*DeleteMemoReactionRequest)(nil), // 25: memos.api.v1.DeleteMemoReactionRequest
	(*Memo_Property)(nil),             // 26: memos.api.v1.Memo.Property
	(*MemoRelation_Memo)(nil),         // 27: memos.api.v1.MemoRelation.Memo
	(State)(0),                        // 28: memos.api.v1.State
	(*timestamppb.Timestamp)(nil),     // 29: google.protobuf.Timestamp
	(*Node)(nil),                      // 30: memos.api.v1.Node
	(*Resource)(nil),                  // 31: memos.api.v1.Resource
	(*Reaction)(nil),                  // 32: memos.api.v1.Reaction
	(Direction)(0),                    // 33: memos.api.v1.Direction
	(*fieldmaskpb.FieldMask)(nil),     // 34: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),             // 35: google.protobuf.Empty
}
var file_api_v1_memo_service_proto_depIdxs = []int32{
	28, // 0: memos.api.v1.Memo.state:type_name -> memos.api.v1.State
	29, // 1: memos.api.v1.Memo.create_time:type_name -> google.protobuf.Timestamp
	29, // 2: memos.api.v1.Memo.update_time:type_name -> google.protobuf.Timestamp
	29, // 3: memos.api.v1.Memo.display_time:type_name -> google.protobuf.Timestamp
	30, // 4: memos.api.v1.Memo.nodes:type_name -> memos.api.v1.Node
	0,  // 5: memos.api.v1.Memo.visibility:type_name -> memos.api.v1.Visibility
	31, // 6: memos.api.v1.Memo.resources:type_name -> memos.api.v1.Resource
	15, // 7: memos.api.v1.Memo.relations:type_name -> memos.api.v1.MemoRelation
	32, // 8: memos.api.v1.Memo.reactions:type_name -> memos.api.v1.Reaction
	26, // 9: memos.api.v1.Memo.property:type_name -> memos.api.v1.Memo.Property
	3,  // 10: memos.api.v1.Memo.location:type_name -> memos.api.v1.Location
	2,  // 11: memos.api.v1.CreateMemoRequest.memo:type_name -> memos.api.v1.Memo
	28, // 12: memos.api.v1.ListMemosRequest.state:type_name -> memos.api.v1.State
	33, // 13: memos.api.v1.ListMemosRequest.direction:type_name -> memos.api.v1.Direction
	2,  // 14: memos.api.v1.ListMemosResponse.memos:type_name -> memos.api.v1.Memo
	2,  // 15: memos.api.v1.UpdateMemoRequest.memo:type_name -> memos.api.v1.Memo
	34, // 16: memos.api.v1.UpdateMemoRequest.update_mask:type_name -> google.protobuf.FieldMask
	31, // 17: memos.api.v1.SetMemoResourcesRequest.resources:type_name -> memos.api.v1.Resource
	31, // 18: memos.api.v1.ListMemoResourcesResponse.resources:type_name -> memos.api.v1.Resource
	27, // 19: memos.api.v1.MemoRelation.memo:type_name -> memos.api.v1.MemoRelation.Memo
	27, // 20: memos.api.v1.MemoRelation.related_memo:type_name -> memos.api.v1.MemoRelation.Memo
	1,  // 21: memos.api.v1.MemoRelation.type:type_name -> memos.api.v1.MemoRelation.Type
	15, // 22: memos.api.v1.SetMemoRelationsRequest.relations:type_name -> memos.api.v1.MemoRelation
	15, // 23: memos.api.v1.ListMemoRelationsResponse.relations:type_name -> memos.api.v1.MemoRelation
	2,  // 24: memos.api.v1.CreateMemoCommentRequest.comment:type_name -> memos.api.v1.Memo
	2,  // 25: memos.api.v1.ListMemoCommentsResponse.memos:type_name -> memos.api.v1.Memo
	32, // 26: memos.api.v1.ListMemoReactionsResponse.reactions:type_name -> memos.api.v1.Reaction
	32, // 27: memos.api.v1.UpsertMemoReactionRequest.reaction:type_name -> memos.api.v1.Reaction
	4,  // 28: memos.api.v1.MemoService.CreateMemo:input_type -> memos.api.v1.CreateMemoRequest
	5,  // 29: memos.api.v1.MemoService.ListMemos:input_type -> memos.api.v1.ListMemosRequest
	7,  // 30: memos.api.v1.MemoService.GetMemo:input_type -> memos.api.v1.GetMemoRequest
	8,  // 31: memos.api.v1.MemoService.UpdateMemo:input_type -> memos.api.v1.UpdateMemoRequest
	9,  // 32: memos.api.v1.MemoService.DeleteMemo:input_type -> memos.api.v1.DeleteMemoRequest
	10, // 33: memos.api.v1.MemoService.RenameMemoTag:input_type -> memos.api.v1.RenameMemoTagRequest
	11, // 34: memos.api.v1.MemoService.DeleteMemoTag:input_type -> memos.api.v1.DeleteMemoTagRequest
	12, // 35: memos.api.v1.MemoService.SetMemoResources:input_type -> memos.api.v1.SetMemoResourcesRequest
	13, // 36: memos.api.v1.MemoService.ListMemoResources:input_type -> memos.api.v1.ListMemoResourcesRequest
	16, // 37: memos.api.v1.MemoService.SetMemoRelations:input_type -> memos.api.v1.SetMemoRelationsRequest
	17, // 38: memos.api.v1.MemoService.ListMemoRelations:input_type -> memos.api.v1.ListMemoRelationsRequest
	19, // 39: memos.api.v1.MemoService.CreateMemoComment:input_type -> memos.api.v1.CreateMemoCommentRequest
	20, // 40: memos.api.v1.MemoService.ListMemoComments:input_type -> memos.api.v1.ListMemoCommentsRequest
	22, // 41: memos.api.v1.MemoService.ListMemoReactions:input_type -> memos.api.v1.ListMemoReactionsRequest
	24, // 42: memos.api.v1.MemoService.UpsertMemoReaction:input_type -> memos.api.v1.UpsertMemoReactionRequest
	25, // 43: memos.api.v1.MemoService.DeleteMemoReaction:input_type -> memos.api.v1.DeleteMemoReactionRequest
	2,  // 44: memos.api.v1.MemoService.CreateMemo:output_type -> memos.api.v1.Memo
	6,  // 45: memos.api.v1.MemoService.ListMemos:output_type -> memos.api.v1.ListMemosResponse
	2,  // 46: memos.api.v1.MemoService.GetMemo:output_type -> memos.api.v1.Memo
	2,  // 47: memos.api.v1.MemoService.UpdateMemo:output_type -> memos.api.v1.Memo
	35, // 48: memos.api.v1.MemoService.DeleteMemo:output_type -> google.protobuf.Empty
	35, // 49: memos.api.v1.MemoService.RenameMemoTag:output_type -> google.protobuf.Empty
	35, // 50: memos.api.v1.MemoService.DeleteMemoTag:output_type -> google.protobuf.Empty
	35, // 51: memos.api.v1.MemoService.SetMemoResources:output_type -> google.protobuf.Empty
	14, // 52: memos.api.v1.MemoService.ListMemoResources:output_type -> memos.api.v1.ListMemoResourcesResponse
	35, // 53: memos.api.v1.MemoService.SetMemoRelations:output_type -> google.protobuf.Empty
	18, // 54: memos.api.v1.MemoService.ListMemoRelations:output_type -> memos.api.v1.ListMemoRelationsResponse
	2,  // 55: memos.api.v1.MemoService.CreateMemoComment:output_type -> memos.api.v1.Memo
	21, // 56: memos.api.v1.MemoService.ListMemoComments:output_type -> memos.api.v1.ListMemoCommentsResponse
	23, // 57: memos.api.v1.MemoService.ListMemoReactions:output_type -> memos.api.v1.ListMemoReactionsResponse
	32, // 58: memos.api.v1.MemoService.UpsertMemoReaction:output_type -> memos.api.v1.Reaction
	35, // 59: memos.api.v1.MemoService.DeleteMemoReaction:output_type -> google.protobuf.Empty
	44, // [44:60] is the sub-list for method output_type
	28, // [28:44] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_v1_memo_service_proto_init() }
func file_api_v1_memo_service_proto_init() {
	if File_api_v1_memo_service_proto != nil {
		return
	}
	file_api_v1_common_proto_init()
	file_api_v1_markdown_service_proto_init()
	file_api_v1_reaction_service_proto_init()
	file_api_v1_resource_service_proto_init()
	file_api_v1_memo_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_memo_service_proto_rawDesc), len(file_api_v1_memo_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_memo_service_proto_goTypes,
		DependencyIndexes: file_api_v1_memo_service_proto_depIdxs,
		EnumInfos:         file_api_v1_memo_service_proto_enumTypes,
		MessageInfos:      file_api_v1_memo_service_proto_msgTypes,
	}.Build()
	File_api_v1_memo_service_proto = out.File
	file_api_v1_memo_service_proto_goTypes = nil
	file_api_v1_memo_service_proto_depIdxs = nil
}
