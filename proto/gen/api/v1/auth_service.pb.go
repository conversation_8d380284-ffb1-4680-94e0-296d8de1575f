// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/auth_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAuthStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthStatusRequest) Reset() {
	*x = GetAuthStatusRequest{}
	mi := &file_api_v1_auth_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStatusRequest) ProtoMessage() {}

func (x *GetAuthStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAuthStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{0}
}

type GetAuthStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthStatusResponse) Reset() {
	*x = GetAuthStatusResponse{}
	mi := &file_api_v1_auth_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStatusResponse) ProtoMessage() {}

func (x *GetAuthStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAuthStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAuthStatusResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type SignInRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Provide one authentication method (username/password or SSO).
	//
	// Types that are valid to be assigned to Method:
	//
	//	*SignInRequest_PasswordCredentials
	//	*SignInRequest_SsoCredentials
	Method isSignInRequest_Method `protobuf_oneof:"method"`
	// Whether the session should never expire.
	NeverExpire   bool `protobuf:"varint,3,opt,name=never_expire,json=neverExpire,proto3" json:"never_expire,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInRequest) Reset() {
	*x = SignInRequest{}
	mi := &file_api_v1_auth_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInRequest) ProtoMessage() {}

func (x *SignInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInRequest.ProtoReflect.Descriptor instead.
func (*SignInRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{2}
}

func (x *SignInRequest) GetMethod() isSignInRequest_Method {
	if x != nil {
		return x.Method
	}
	return nil
}

func (x *SignInRequest) GetPasswordCredentials() *PasswordCredentials {
	if x != nil {
		if x, ok := x.Method.(*SignInRequest_PasswordCredentials); ok {
			return x.PasswordCredentials
		}
	}
	return nil
}

func (x *SignInRequest) GetSsoCredentials() *SSOCredentials {
	if x != nil {
		if x, ok := x.Method.(*SignInRequest_SsoCredentials); ok {
			return x.SsoCredentials
		}
	}
	return nil
}

func (x *SignInRequest) GetNeverExpire() bool {
	if x != nil {
		return x.NeverExpire
	}
	return false
}

type isSignInRequest_Method interface {
	isSignInRequest_Method()
}

type SignInRequest_PasswordCredentials struct {
	// Username and password authentication method.
	PasswordCredentials *PasswordCredentials `protobuf:"bytes,1,opt,name=password_credentials,json=passwordCredentials,proto3,oneof"`
}

type SignInRequest_SsoCredentials struct {
	// SSO provider authentication method.
	SsoCredentials *SSOCredentials `protobuf:"bytes,2,opt,name=sso_credentials,json=ssoCredentials,proto3,oneof"`
}

func (*SignInRequest_PasswordCredentials) isSignInRequest_Method() {}

func (*SignInRequest_SsoCredentials) isSignInRequest_Method() {}

type PasswordCredentials struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The username to sign in with.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password to sign in with.
	Password      string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PasswordCredentials) Reset() {
	*x = PasswordCredentials{}
	mi := &file_api_v1_auth_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PasswordCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PasswordCredentials) ProtoMessage() {}

func (x *PasswordCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PasswordCredentials.ProtoReflect.Descriptor instead.
func (*PasswordCredentials) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{3}
}

func (x *PasswordCredentials) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PasswordCredentials) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type SSOCredentials struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the SSO provider.
	IdpId int32 `protobuf:"varint,1,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
	// The code to sign in with.
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// The redirect URI.
	RedirectUri   string `protobuf:"bytes,3,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SSOCredentials) Reset() {
	*x = SSOCredentials{}
	mi := &file_api_v1_auth_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SSOCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSOCredentials) ProtoMessage() {}

func (x *SSOCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSOCredentials.ProtoReflect.Descriptor instead.
func (*SSOCredentials) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{4}
}

func (x *SSOCredentials) GetIdpId() int32 {
	if x != nil {
		return x.IdpId
	}
	return 0
}

func (x *SSOCredentials) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SSOCredentials) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

type SignUpRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The username to sign up with.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password to sign up with.
	Password      string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignUpRequest) Reset() {
	*x = SignUpRequest{}
	mi := &file_api_v1_auth_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpRequest) ProtoMessage() {}

func (x *SignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpRequest.ProtoReflect.Descriptor instead.
func (*SignUpRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{5}
}

func (x *SignUpRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SignUpRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type SignOutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignOutRequest) Reset() {
	*x = SignOutRequest{}
	mi := &file_api_v1_auth_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignOutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignOutRequest) ProtoMessage() {}

func (x *SignOutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_auth_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignOutRequest.ProtoReflect.Descriptor instead.
func (*SignOutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_auth_service_proto_rawDescGZIP(), []int{6}
}

var File_api_v1_auth_service_proto protoreflect.FileDescriptor

const file_api_v1_auth_service_proto_rawDesc = "" +
	"\n" +
	"\x19api/v1/auth_service.proto\x12\fmemos.api.v1\x1a\x19api/v1/user_service.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"\x16\n" +
	"\x14GetAuthStatusRequest\"?\n" +
	"\x15GetAuthStatusResponse\x12&\n" +
	"\x04user\x18\x01 \x01(\v2\x12.memos.api.v1.UserR\x04user\"\xdd\x01\n" +
	"\rSignInRequest\x12V\n" +
	"\x14password_credentials\x18\x01 \x01(\v2!.memos.api.v1.PasswordCredentialsH\x00R\x13passwordCredentials\x12G\n" +
	"\x0fsso_credentials\x18\x02 \x01(\v2\x1c.memos.api.v1.SSOCredentialsH\x00R\x0essoCredentials\x12!\n" +
	"\fnever_expire\x18\x03 \x01(\bR\vneverExpireB\b\n" +
	"\x06method\"M\n" +
	"\x13PasswordCredentials\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"^\n" +
	"\x0eSSOCredentials\x12\x15\n" +
	"\x06idp_id\x18\x01 \x01(\x05R\x05idpId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12!\n" +
	"\fredirect_uri\x18\x03 \x01(\tR\vredirectUri\"G\n" +
	"\rSignUpRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"\x10\n" +
	"\x0eSignOutRequest2\x82\x03\n" +
	"\vAuthService\x12d\n" +
	"\rGetAuthStatus\x12\".memos.api.v1.GetAuthStatusRequest\x1a\x12.memos.api.v1.User\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x13/api/v1/auth/status\x12V\n" +
	"\x06SignIn\x12\x1b.memos.api.v1.SignInRequest\x1a\x12.memos.api.v1.User\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x13/api/v1/auth/signin\x12V\n" +
	"\x06SignUp\x12\x1b.memos.api.v1.SignUpRequest\x1a\x12.memos.api.v1.User\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x13/api/v1/auth/signup\x12]\n" +
	"\aSignOut\x12\x1c.memos.api.v1.SignOutRequest\x1a\x16.google.protobuf.Empty\"\x1c\x82\xd3\xe4\x93\x02\x16\"\x14/api/v1/auth/signoutB\xa8\x01\n" +
	"\x10com.memos.api.v1B\x10AuthServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_auth_service_proto_rawDescOnce sync.Once
	file_api_v1_auth_service_proto_rawDescData []byte
)

func file_api_v1_auth_service_proto_rawDescGZIP() []byte {
	file_api_v1_auth_service_proto_rawDescOnce.Do(func() {
		file_api_v1_auth_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_auth_service_proto_rawDesc), len(file_api_v1_auth_service_proto_rawDesc)))
	})
	return file_api_v1_auth_service_proto_rawDescData
}

var file_api_v1_auth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_v1_auth_service_proto_goTypes = []any{
	(*GetAuthStatusRequest)(nil),  // 0: memos.api.v1.GetAuthStatusRequest
	(*GetAuthStatusResponse)(nil), // 1: memos.api.v1.GetAuthStatusResponse
	(*SignInRequest)(nil),         // 2: memos.api.v1.SignInRequest
	(*PasswordCredentials)(nil),   // 3: memos.api.v1.PasswordCredentials
	(*SSOCredentials)(nil),        // 4: memos.api.v1.SSOCredentials
	(*SignUpRequest)(nil),         // 5: memos.api.v1.SignUpRequest
	(*SignOutRequest)(nil),        // 6: memos.api.v1.SignOutRequest
	(*User)(nil),                  // 7: memos.api.v1.User
	(*emptypb.Empty)(nil),         // 8: google.protobuf.Empty
}
var file_api_v1_auth_service_proto_depIdxs = []int32{
	7, // 0: memos.api.v1.GetAuthStatusResponse.user:type_name -> memos.api.v1.User
	3, // 1: memos.api.v1.SignInRequest.password_credentials:type_name -> memos.api.v1.PasswordCredentials
	4, // 2: memos.api.v1.SignInRequest.sso_credentials:type_name -> memos.api.v1.SSOCredentials
	0, // 3: memos.api.v1.AuthService.GetAuthStatus:input_type -> memos.api.v1.GetAuthStatusRequest
	2, // 4: memos.api.v1.AuthService.SignIn:input_type -> memos.api.v1.SignInRequest
	5, // 5: memos.api.v1.AuthService.SignUp:input_type -> memos.api.v1.SignUpRequest
	6, // 6: memos.api.v1.AuthService.SignOut:input_type -> memos.api.v1.SignOutRequest
	7, // 7: memos.api.v1.AuthService.GetAuthStatus:output_type -> memos.api.v1.User
	7, // 8: memos.api.v1.AuthService.SignIn:output_type -> memos.api.v1.User
	7, // 9: memos.api.v1.AuthService.SignUp:output_type -> memos.api.v1.User
	8, // 10: memos.api.v1.AuthService.SignOut:output_type -> google.protobuf.Empty
	7, // [7:11] is the sub-list for method output_type
	3, // [3:7] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_v1_auth_service_proto_init() }
func file_api_v1_auth_service_proto_init() {
	if File_api_v1_auth_service_proto != nil {
		return
	}
	file_api_v1_user_service_proto_init()
	file_api_v1_auth_service_proto_msgTypes[2].OneofWrappers = []any{
		(*SignInRequest_PasswordCredentials)(nil),
		(*SignInRequest_SsoCredentials)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_auth_service_proto_rawDesc), len(file_api_v1_auth_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_auth_service_proto_goTypes,
		DependencyIndexes: file_api_v1_auth_service_proto_depIdxs,
		MessageInfos:      file_api_v1_auth_service_proto_msgTypes,
	}.Build()
	File_api_v1_auth_service_proto = out.File
	file_api_v1_auth_service_proto_goTypes = nil
	file_api_v1_auth_service_proto_depIdxs = nil
}
