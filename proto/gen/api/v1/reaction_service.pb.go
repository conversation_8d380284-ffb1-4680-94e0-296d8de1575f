// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/reaction_service.proto

package apiv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reaction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the creator.
	// Format: users/{user}
	Creator string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	// The content identifier.
	// For memo, it should be the `Memo.name`.
	ContentId     string `protobuf:"bytes,3,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	ReactionType  string `protobuf:"bytes,4,opt,name=reaction_type,json=reactionType,proto3" json:"reaction_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Reaction) Reset() {
	*x = Reaction{}
	mi := &file_api_v1_reaction_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reaction) ProtoMessage() {}

func (x *Reaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_reaction_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reaction.ProtoReflect.Descriptor instead.
func (*Reaction) Descriptor() ([]byte, []int) {
	return file_api_v1_reaction_service_proto_rawDescGZIP(), []int{0}
}

func (x *Reaction) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Reaction) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Reaction) GetContentId() string {
	if x != nil {
		return x.ContentId
	}
	return ""
}

func (x *Reaction) GetReactionType() string {
	if x != nil {
		return x.ReactionType
	}
	return ""
}

var File_api_v1_reaction_service_proto protoreflect.FileDescriptor

const file_api_v1_reaction_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/v1/reaction_service.proto\x12\fmemos.api.v1\"x\n" +
	"\bReaction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x18\n" +
	"\acreator\x18\x02 \x01(\tR\acreator\x12\x1d\n" +
	"\n" +
	"content_id\x18\x03 \x01(\tR\tcontentId\x12#\n" +
	"\rreaction_type\x18\x04 \x01(\tR\freactionTypeB\xac\x01\n" +
	"\x10com.memos.api.v1B\x14ReactionServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_reaction_service_proto_rawDescOnce sync.Once
	file_api_v1_reaction_service_proto_rawDescData []byte
)

func file_api_v1_reaction_service_proto_rawDescGZIP() []byte {
	file_api_v1_reaction_service_proto_rawDescOnce.Do(func() {
		file_api_v1_reaction_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_reaction_service_proto_rawDesc), len(file_api_v1_reaction_service_proto_rawDesc)))
	})
	return file_api_v1_reaction_service_proto_rawDescData
}

var file_api_v1_reaction_service_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_v1_reaction_service_proto_goTypes = []any{
	(*Reaction)(nil), // 0: memos.api.v1.Reaction
}
var file_api_v1_reaction_service_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_v1_reaction_service_proto_init() }
func file_api_v1_reaction_service_proto_init() {
	if File_api_v1_reaction_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_reaction_service_proto_rawDesc), len(file_api_v1_reaction_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_v1_reaction_service_proto_goTypes,
		DependencyIndexes: file_api_v1_reaction_service_proto_depIdxs,
		MessageInfos:      file_api_v1_reaction_service_proto_msgTypes,
	}.Build()
	File_api_v1_reaction_service_proto = out.File
	file_api_v1_reaction_service_proto_goTypes = nil
	file_api_v1_reaction_service_proto_depIdxs = nil
}
