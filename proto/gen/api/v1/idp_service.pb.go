// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/idp_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IdentityProvider_Type int32

const (
	IdentityProvider_TYPE_UNSPECIFIED IdentityProvider_Type = 0
	IdentityProvider_OAUTH2           IdentityProvider_Type = 1
)

// Enum value maps for IdentityProvider_Type.
var (
	IdentityProvider_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "OAUTH2",
	}
	IdentityProvider_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"OAUTH2":           1,
	}
)

func (x IdentityProvider_Type) Enum() *IdentityProvider_Type {
	p := new(IdentityProvider_Type)
	*p = x
	return p
}

func (x IdentityProvider_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityProvider_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_idp_service_proto_enumTypes[0].Descriptor()
}

func (IdentityProvider_Type) Type() protoreflect.EnumType {
	return &file_api_v1_idp_service_proto_enumTypes[0]
}

func (x IdentityProvider_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityProvider_Type.Descriptor instead.
func (IdentityProvider_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{0, 0}
}

type IdentityProvider struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the identityProvider.
	// Format: identityProviders/{id}, id is the system generated auto-incremented id.
	Name             string                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type             IdentityProvider_Type   `protobuf:"varint,2,opt,name=type,proto3,enum=memos.api.v1.IdentityProvider_Type" json:"type,omitempty"`
	Title            string                  `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	IdentifierFilter string                  `protobuf:"bytes,4,opt,name=identifier_filter,json=identifierFilter,proto3" json:"identifier_filter,omitempty"`
	Config           *IdentityProviderConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IdentityProvider) Reset() {
	*x = IdentityProvider{}
	mi := &file_api_v1_idp_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IdentityProvider) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityProvider) ProtoMessage() {}

func (x *IdentityProvider) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityProvider.ProtoReflect.Descriptor instead.
func (*IdentityProvider) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{0}
}

func (x *IdentityProvider) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdentityProvider) GetType() IdentityProvider_Type {
	if x != nil {
		return x.Type
	}
	return IdentityProvider_TYPE_UNSPECIFIED
}

func (x *IdentityProvider) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *IdentityProvider) GetIdentifierFilter() string {
	if x != nil {
		return x.IdentifierFilter
	}
	return ""
}

func (x *IdentityProvider) GetConfig() *IdentityProviderConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type IdentityProviderConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Config:
	//
	//	*IdentityProviderConfig_Oauth2Config
	Config        isIdentityProviderConfig_Config `protobuf_oneof:"config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IdentityProviderConfig) Reset() {
	*x = IdentityProviderConfig{}
	mi := &file_api_v1_idp_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IdentityProviderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityProviderConfig) ProtoMessage() {}

func (x *IdentityProviderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityProviderConfig.ProtoReflect.Descriptor instead.
func (*IdentityProviderConfig) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{1}
}

func (x *IdentityProviderConfig) GetConfig() isIdentityProviderConfig_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *IdentityProviderConfig) GetOauth2Config() *OAuth2Config {
	if x != nil {
		if x, ok := x.Config.(*IdentityProviderConfig_Oauth2Config); ok {
			return x.Oauth2Config
		}
	}
	return nil
}

type isIdentityProviderConfig_Config interface {
	isIdentityProviderConfig_Config()
}

type IdentityProviderConfig_Oauth2Config struct {
	Oauth2Config *OAuth2Config `protobuf:"bytes,1,opt,name=oauth2_config,json=oauth2Config,proto3,oneof"`
}

func (*IdentityProviderConfig_Oauth2Config) isIdentityProviderConfig_Config() {}

type FieldMapping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Identifier    string                 `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	DisplayName   string                 `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,4,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldMapping) Reset() {
	*x = FieldMapping{}
	mi := &file_api_v1_idp_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldMapping) ProtoMessage() {}

func (x *FieldMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldMapping.ProtoReflect.Descriptor instead.
func (*FieldMapping) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{2}
}

func (x *FieldMapping) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *FieldMapping) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *FieldMapping) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *FieldMapping) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type OAuth2Config struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      string                 `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	AuthUrl       string                 `protobuf:"bytes,3,opt,name=auth_url,json=authUrl,proto3" json:"auth_url,omitempty"`
	TokenUrl      string                 `protobuf:"bytes,4,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	UserInfoUrl   string                 `protobuf:"bytes,5,opt,name=user_info_url,json=userInfoUrl,proto3" json:"user_info_url,omitempty"`
	Scopes        []string               `protobuf:"bytes,6,rep,name=scopes,proto3" json:"scopes,omitempty"`
	FieldMapping  *FieldMapping          `protobuf:"bytes,7,opt,name=field_mapping,json=fieldMapping,proto3" json:"field_mapping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OAuth2Config) Reset() {
	*x = OAuth2Config{}
	mi := &file_api_v1_idp_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OAuth2Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuth2Config) ProtoMessage() {}

func (x *OAuth2Config) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuth2Config.ProtoReflect.Descriptor instead.
func (*OAuth2Config) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{3}
}

func (x *OAuth2Config) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OAuth2Config) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *OAuth2Config) GetAuthUrl() string {
	if x != nil {
		return x.AuthUrl
	}
	return ""
}

func (x *OAuth2Config) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *OAuth2Config) GetUserInfoUrl() string {
	if x != nil {
		return x.UserInfoUrl
	}
	return ""
}

func (x *OAuth2Config) GetScopes() []string {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *OAuth2Config) GetFieldMapping() *FieldMapping {
	if x != nil {
		return x.FieldMapping
	}
	return nil
}

type ListIdentityProvidersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListIdentityProvidersRequest) Reset() {
	*x = ListIdentityProvidersRequest{}
	mi := &file_api_v1_idp_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIdentityProvidersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIdentityProvidersRequest) ProtoMessage() {}

func (x *ListIdentityProvidersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIdentityProvidersRequest.ProtoReflect.Descriptor instead.
func (*ListIdentityProvidersRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{4}
}

type ListIdentityProvidersResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdentityProviders []*IdentityProvider    `protobuf:"bytes,1,rep,name=identity_providers,json=identityProviders,proto3" json:"identity_providers,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListIdentityProvidersResponse) Reset() {
	*x = ListIdentityProvidersResponse{}
	mi := &file_api_v1_idp_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIdentityProvidersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIdentityProvidersResponse) ProtoMessage() {}

func (x *ListIdentityProvidersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIdentityProvidersResponse.ProtoReflect.Descriptor instead.
func (*ListIdentityProvidersResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListIdentityProvidersResponse) GetIdentityProviders() []*IdentityProvider {
	if x != nil {
		return x.IdentityProviders
	}
	return nil
}

type GetIdentityProviderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the identityProvider to get.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIdentityProviderRequest) Reset() {
	*x = GetIdentityProviderRequest{}
	mi := &file_api_v1_idp_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentityProviderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityProviderRequest) ProtoMessage() {}

func (x *GetIdentityProviderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityProviderRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityProviderRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetIdentityProviderRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreateIdentityProviderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The identityProvider to create.
	IdentityProvider *IdentityProvider `protobuf:"bytes,1,opt,name=identity_provider,json=identityProvider,proto3" json:"identity_provider,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateIdentityProviderRequest) Reset() {
	*x = CreateIdentityProviderRequest{}
	mi := &file_api_v1_idp_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIdentityProviderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIdentityProviderRequest) ProtoMessage() {}

func (x *CreateIdentityProviderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIdentityProviderRequest.ProtoReflect.Descriptor instead.
func (*CreateIdentityProviderRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateIdentityProviderRequest) GetIdentityProvider() *IdentityProvider {
	if x != nil {
		return x.IdentityProvider
	}
	return nil
}

type UpdateIdentityProviderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The identityProvider to update.
	IdentityProvider *IdentityProvider `protobuf:"bytes,1,opt,name=identity_provider,json=identityProvider,proto3" json:"identity_provider,omitempty"`
	// The update mask applies to the resource. Only the top level fields of
	// IdentityProvider are supported.
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIdentityProviderRequest) Reset() {
	*x = UpdateIdentityProviderRequest{}
	mi := &file_api_v1_idp_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIdentityProviderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIdentityProviderRequest) ProtoMessage() {}

func (x *UpdateIdentityProviderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIdentityProviderRequest.ProtoReflect.Descriptor instead.
func (*UpdateIdentityProviderRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateIdentityProviderRequest) GetIdentityProvider() *IdentityProvider {
	if x != nil {
		return x.IdentityProvider
	}
	return nil
}

func (x *UpdateIdentityProviderRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteIdentityProviderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the identityProvider to delete.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIdentityProviderRequest) Reset() {
	*x = DeleteIdentityProviderRequest{}
	mi := &file_api_v1_idp_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIdentityProviderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIdentityProviderRequest) ProtoMessage() {}

func (x *DeleteIdentityProviderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_idp_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIdentityProviderRequest.ProtoReflect.Descriptor instead.
func (*DeleteIdentityProviderRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_idp_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteIdentityProviderRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_idp_service_proto protoreflect.FileDescriptor

const file_api_v1_idp_service_proto_rawDesc = "" +
	"\n" +
	"\x18api/v1/idp_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\"\x8a\x02\n" +
	"\x10IdentityProvider\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x127\n" +
	"\x04type\x18\x02 \x01(\x0e2#.memos.api.v1.IdentityProvider.TypeR\x04type\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12+\n" +
	"\x11identifier_filter\x18\x04 \x01(\tR\x10identifierFilter\x12<\n" +
	"\x06config\x18\x05 \x01(\v2$.memos.api.v1.IdentityProviderConfigR\x06config\"(\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06OAUTH2\x10\x01\"e\n" +
	"\x16IdentityProviderConfig\x12A\n" +
	"\roauth2_config\x18\x01 \x01(\v2\x1a.memos.api.v1.OAuth2ConfigH\x00R\foauth2ConfigB\b\n" +
	"\x06config\"\x86\x01\n" +
	"\fFieldMapping\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12!\n" +
	"\fdisplay_name\x18\x02 \x01(\tR\vdisplayName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x04 \x01(\tR\tavatarUrl\"\x85\x02\n" +
	"\fOAuth2Config\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x02 \x01(\tR\fclientSecret\x12\x19\n" +
	"\bauth_url\x18\x03 \x01(\tR\aauthUrl\x12\x1b\n" +
	"\ttoken_url\x18\x04 \x01(\tR\btokenUrl\x12\"\n" +
	"\ruser_info_url\x18\x05 \x01(\tR\vuserInfoUrl\x12\x16\n" +
	"\x06scopes\x18\x06 \x03(\tR\x06scopes\x12?\n" +
	"\rfield_mapping\x18\a \x01(\v2\x1a.memos.api.v1.FieldMappingR\ffieldMapping\"\x1e\n" +
	"\x1cListIdentityProvidersRequest\"n\n" +
	"\x1dListIdentityProvidersResponse\x12M\n" +
	"\x12identity_providers\x18\x01 \x03(\v2\x1e.memos.api.v1.IdentityProviderR\x11identityProviders\"0\n" +
	"\x1aGetIdentityProviderRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"l\n" +
	"\x1dCreateIdentityProviderRequest\x12K\n" +
	"\x11identity_provider\x18\x01 \x01(\v2\x1e.memos.api.v1.IdentityProviderR\x10identityProvider\"\xa9\x01\n" +
	"\x1dUpdateIdentityProviderRequest\x12K\n" +
	"\x11identity_provider\x18\x01 \x01(\v2\x1e.memos.api.v1.IdentityProviderR\x10identityProvider\x12;\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"3\n" +
	"\x1dDeleteIdentityProviderRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name2\xce\x06\n" +
	"\x17IdentityProviderService\x12\x93\x01\n" +
	"\x15ListIdentityProviders\x12*.memos.api.v1.ListIdentityProvidersRequest\x1a+.memos.api.v1.ListIdentityProvidersResponse\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/api/v1/identityProviders\x12\x92\x01\n" +
	"\x13GetIdentityProvider\x12(.memos.api.v1.GetIdentityProviderRequest\x1a\x1e.memos.api.v1.IdentityProvider\"1\xdaA\x04name\x82\xd3\xe4\x93\x02$\x12\"/api/v1/{name=identityProviders/*}\x12\x9b\x01\n" +
	"\x16CreateIdentityProvider\x12+.memos.api.v1.CreateIdentityProviderRequest\x1a\x1e.memos.api.v1.IdentityProvider\"4\x82\xd3\xe4\x93\x02.:\x11identity_provider\"\x19/api/v1/identityProviders\x12\xd6\x01\n" +
	"\x16UpdateIdentityProvider\x12+.memos.api.v1.UpdateIdentityProviderRequest\x1a\x1e.memos.api.v1.IdentityProvider\"o\xdaA\x1didentity_provider,update_mask\x82\xd3\xe4\x93\x02I:\x11identity_provider24/api/v1/{identity_provider.name=identityProviders/*}\x12\x90\x01\n" +
	"\x16DeleteIdentityProvider\x12+.memos.api.v1.DeleteIdentityProviderRequest\x1a\x16.google.protobuf.Empty\"1\xdaA\x04name\x82\xd3\xe4\x93\x02$*\"/api/v1/{name=identityProviders/*}B\xa7\x01\n" +
	"\x10com.memos.api.v1B\x0fIdpServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_idp_service_proto_rawDescOnce sync.Once
	file_api_v1_idp_service_proto_rawDescData []byte
)

func file_api_v1_idp_service_proto_rawDescGZIP() []byte {
	file_api_v1_idp_service_proto_rawDescOnce.Do(func() {
		file_api_v1_idp_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_idp_service_proto_rawDesc), len(file_api_v1_idp_service_proto_rawDesc)))
	})
	return file_api_v1_idp_service_proto_rawDescData
}

var file_api_v1_idp_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_v1_idp_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_v1_idp_service_proto_goTypes = []any{
	(IdentityProvider_Type)(0),            // 0: memos.api.v1.IdentityProvider.Type
	(*IdentityProvider)(nil),              // 1: memos.api.v1.IdentityProvider
	(*IdentityProviderConfig)(nil),        // 2: memos.api.v1.IdentityProviderConfig
	(*FieldMapping)(nil),                  // 3: memos.api.v1.FieldMapping
	(*OAuth2Config)(nil),                  // 4: memos.api.v1.OAuth2Config
	(*ListIdentityProvidersRequest)(nil),  // 5: memos.api.v1.ListIdentityProvidersRequest
	(*ListIdentityProvidersResponse)(nil), // 6: memos.api.v1.ListIdentityProvidersResponse
	(*GetIdentityProviderRequest)(nil),    // 7: memos.api.v1.GetIdentityProviderRequest
	(*CreateIdentityProviderRequest)(nil), // 8: memos.api.v1.CreateIdentityProviderRequest
	(*UpdateIdentityProviderRequest)(nil), // 9: memos.api.v1.UpdateIdentityProviderRequest
	(*DeleteIdentityProviderRequest)(nil), // 10: memos.api.v1.DeleteIdentityProviderRequest
	(*fieldmaskpb.FieldMask)(nil),         // 11: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),                 // 12: google.protobuf.Empty
}
var file_api_v1_idp_service_proto_depIdxs = []int32{
	0,  // 0: memos.api.v1.IdentityProvider.type:type_name -> memos.api.v1.IdentityProvider.Type
	2,  // 1: memos.api.v1.IdentityProvider.config:type_name -> memos.api.v1.IdentityProviderConfig
	4,  // 2: memos.api.v1.IdentityProviderConfig.oauth2_config:type_name -> memos.api.v1.OAuth2Config
	3,  // 3: memos.api.v1.OAuth2Config.field_mapping:type_name -> memos.api.v1.FieldMapping
	1,  // 4: memos.api.v1.ListIdentityProvidersResponse.identity_providers:type_name -> memos.api.v1.IdentityProvider
	1,  // 5: memos.api.v1.CreateIdentityProviderRequest.identity_provider:type_name -> memos.api.v1.IdentityProvider
	1,  // 6: memos.api.v1.UpdateIdentityProviderRequest.identity_provider:type_name -> memos.api.v1.IdentityProvider
	11, // 7: memos.api.v1.UpdateIdentityProviderRequest.update_mask:type_name -> google.protobuf.FieldMask
	5,  // 8: memos.api.v1.IdentityProviderService.ListIdentityProviders:input_type -> memos.api.v1.ListIdentityProvidersRequest
	7,  // 9: memos.api.v1.IdentityProviderService.GetIdentityProvider:input_type -> memos.api.v1.GetIdentityProviderRequest
	8,  // 10: memos.api.v1.IdentityProviderService.CreateIdentityProvider:input_type -> memos.api.v1.CreateIdentityProviderRequest
	9,  // 11: memos.api.v1.IdentityProviderService.UpdateIdentityProvider:input_type -> memos.api.v1.UpdateIdentityProviderRequest
	10, // 12: memos.api.v1.IdentityProviderService.DeleteIdentityProvider:input_type -> memos.api.v1.DeleteIdentityProviderRequest
	6,  // 13: memos.api.v1.IdentityProviderService.ListIdentityProviders:output_type -> memos.api.v1.ListIdentityProvidersResponse
	1,  // 14: memos.api.v1.IdentityProviderService.GetIdentityProvider:output_type -> memos.api.v1.IdentityProvider
	1,  // 15: memos.api.v1.IdentityProviderService.CreateIdentityProvider:output_type -> memos.api.v1.IdentityProvider
	1,  // 16: memos.api.v1.IdentityProviderService.UpdateIdentityProvider:output_type -> memos.api.v1.IdentityProvider
	12, // 17: memos.api.v1.IdentityProviderService.DeleteIdentityProvider:output_type -> google.protobuf.Empty
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_v1_idp_service_proto_init() }
func file_api_v1_idp_service_proto_init() {
	if File_api_v1_idp_service_proto != nil {
		return
	}
	file_api_v1_idp_service_proto_msgTypes[1].OneofWrappers = []any{
		(*IdentityProviderConfig_Oauth2Config)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_idp_service_proto_rawDesc), len(file_api_v1_idp_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_idp_service_proto_goTypes,
		DependencyIndexes: file_api_v1_idp_service_proto_depIdxs,
		EnumInfos:         file_api_v1_idp_service_proto_enumTypes,
		MessageInfos:      file_api_v1_idp_service_proto_msgTypes,
	}.Build()
	File_api_v1_idp_service_proto = out.File
	file_api_v1_idp_service_proto_goTypes = nil
	file_api_v1_idp_service_proto_depIdxs = nil
}
