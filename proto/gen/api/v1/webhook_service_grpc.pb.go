// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/v1/webhook_service.proto

package apiv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WebhookService_CreateWebhook_FullMethodName = "/memos.api.v1.WebhookService/CreateWebhook"
	WebhookService_GetWebhook_FullMethodName    = "/memos.api.v1.WebhookService/GetWebhook"
	WebhookService_ListWebhooks_FullMethodName  = "/memos.api.v1.WebhookService/ListWebhooks"
	WebhookService_UpdateWebhook_FullMethodName = "/memos.api.v1.WebhookService/UpdateWebhook"
	WebhookService_DeleteWebhook_FullMethodName = "/memos.api.v1.WebhookService/DeleteWebhook"
)

// WebhookServiceClient is the client API for WebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookServiceClient interface {
	// CreateWebhook creates a new webhook.
	CreateWebhook(ctx context.Context, in *CreateWebhookRequest, opts ...grpc.CallOption) (*Webhook, error)
	// GetWebhook returns a webhook by id.
	GetWebhook(ctx context.Context, in *GetWebhookRequest, opts ...grpc.CallOption) (*Webhook, error)
	// ListWebhooks returns a list of webhooks.
	ListWebhooks(ctx context.Context, in *ListWebhooksRequest, opts ...grpc.CallOption) (*ListWebhooksResponse, error)
	// UpdateWebhook updates a webhook.
	UpdateWebhook(ctx context.Context, in *UpdateWebhookRequest, opts ...grpc.CallOption) (*Webhook, error)
	// DeleteWebhook deletes a webhook by id.
	DeleteWebhook(ctx context.Context, in *DeleteWebhookRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type webhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookServiceClient(cc grpc.ClientConnInterface) WebhookServiceClient {
	return &webhookServiceClient{cc}
}

func (c *webhookServiceClient) CreateWebhook(ctx context.Context, in *CreateWebhookRequest, opts ...grpc.CallOption) (*Webhook, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Webhook)
	err := c.cc.Invoke(ctx, WebhookService_CreateWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookServiceClient) GetWebhook(ctx context.Context, in *GetWebhookRequest, opts ...grpc.CallOption) (*Webhook, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Webhook)
	err := c.cc.Invoke(ctx, WebhookService_GetWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookServiceClient) ListWebhooks(ctx context.Context, in *ListWebhooksRequest, opts ...grpc.CallOption) (*ListWebhooksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWebhooksResponse)
	err := c.cc.Invoke(ctx, WebhookService_ListWebhooks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookServiceClient) UpdateWebhook(ctx context.Context, in *UpdateWebhookRequest, opts ...grpc.CallOption) (*Webhook, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Webhook)
	err := c.cc.Invoke(ctx, WebhookService_UpdateWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webhookServiceClient) DeleteWebhook(ctx context.Context, in *DeleteWebhookRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, WebhookService_DeleteWebhook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookServiceServer is the server API for WebhookService service.
// All implementations must embed UnimplementedWebhookServiceServer
// for forward compatibility.
type WebhookServiceServer interface {
	// CreateWebhook creates a new webhook.
	CreateWebhook(context.Context, *CreateWebhookRequest) (*Webhook, error)
	// GetWebhook returns a webhook by id.
	GetWebhook(context.Context, *GetWebhookRequest) (*Webhook, error)
	// ListWebhooks returns a list of webhooks.
	ListWebhooks(context.Context, *ListWebhooksRequest) (*ListWebhooksResponse, error)
	// UpdateWebhook updates a webhook.
	UpdateWebhook(context.Context, *UpdateWebhookRequest) (*Webhook, error)
	// DeleteWebhook deletes a webhook by id.
	DeleteWebhook(context.Context, *DeleteWebhookRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedWebhookServiceServer()
}

// UnimplementedWebhookServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWebhookServiceServer struct{}

func (UnimplementedWebhookServiceServer) CreateWebhook(context.Context, *CreateWebhookRequest) (*Webhook, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWebhook not implemented")
}
func (UnimplementedWebhookServiceServer) GetWebhook(context.Context, *GetWebhookRequest) (*Webhook, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebhook not implemented")
}
func (UnimplementedWebhookServiceServer) ListWebhooks(context.Context, *ListWebhooksRequest) (*ListWebhooksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWebhooks not implemented")
}
func (UnimplementedWebhookServiceServer) UpdateWebhook(context.Context, *UpdateWebhookRequest) (*Webhook, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWebhook not implemented")
}
func (UnimplementedWebhookServiceServer) DeleteWebhook(context.Context, *DeleteWebhookRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWebhook not implemented")
}
func (UnimplementedWebhookServiceServer) mustEmbedUnimplementedWebhookServiceServer() {}
func (UnimplementedWebhookServiceServer) testEmbeddedByValue()                        {}

// UnsafeWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookServiceServer will
// result in compilation errors.
type UnsafeWebhookServiceServer interface {
	mustEmbedUnimplementedWebhookServiceServer()
}

func RegisterWebhookServiceServer(s grpc.ServiceRegistrar, srv WebhookServiceServer) {
	// If the following call pancis, it indicates UnimplementedWebhookServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WebhookService_ServiceDesc, srv)
}

func _WebhookService_CreateWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).CreateWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_CreateWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).CreateWebhook(ctx, req.(*CreateWebhookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookService_GetWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).GetWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_GetWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).GetWebhook(ctx, req.(*GetWebhookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookService_ListWebhooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWebhooksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).ListWebhooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_ListWebhooks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).ListWebhooks(ctx, req.(*ListWebhooksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookService_UpdateWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).UpdateWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_UpdateWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).UpdateWebhook(ctx, req.(*UpdateWebhookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebhookService_DeleteWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).DeleteWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebhookService_DeleteWebhook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).DeleteWebhook(ctx, req.(*DeleteWebhookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WebhookService_ServiceDesc is the grpc.ServiceDesc for WebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "memos.api.v1.WebhookService",
	HandlerType: (*WebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWebhook",
			Handler:    _WebhookService_CreateWebhook_Handler,
		},
		{
			MethodName: "GetWebhook",
			Handler:    _WebhookService_GetWebhook_Handler,
		},
		{
			MethodName: "ListWebhooks",
			Handler:    _WebhookService_ListWebhooks_Handler,
		},
		{
			MethodName: "UpdateWebhook",
			Handler:    _WebhookService_UpdateWebhook_Handler,
		},
		{
			MethodName: "DeleteWebhook",
			Handler:    _WebhookService_DeleteWebhook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/v1/webhook_service.proto",
}
