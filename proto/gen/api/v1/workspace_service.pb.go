// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/workspace_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkspaceProfile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of instance owner.
	// Format: users/{user}
	Owner string `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	// version is the current version of instance
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// mode is the instance mode (e.g. "prod", "dev" or "demo").
	Mode string `protobuf:"bytes,3,opt,name=mode,proto3" json:"mode,omitempty"`
	// instance_url is the URL of the instance.
	InstanceUrl   string `protobuf:"bytes,6,opt,name=instance_url,json=instanceUrl,proto3" json:"instance_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkspaceProfile) Reset() {
	*x = WorkspaceProfile{}
	mi := &file_api_v1_workspace_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkspaceProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceProfile) ProtoMessage() {}

func (x *WorkspaceProfile) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_workspace_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceProfile.ProtoReflect.Descriptor instead.
func (*WorkspaceProfile) Descriptor() ([]byte, []int) {
	return file_api_v1_workspace_service_proto_rawDescGZIP(), []int{0}
}

func (x *WorkspaceProfile) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *WorkspaceProfile) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *WorkspaceProfile) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *WorkspaceProfile) GetInstanceUrl() string {
	if x != nil {
		return x.InstanceUrl
	}
	return ""
}

type GetWorkspaceProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkspaceProfileRequest) Reset() {
	*x = GetWorkspaceProfileRequest{}
	mi := &file_api_v1_workspace_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkspaceProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceProfileRequest) ProtoMessage() {}

func (x *GetWorkspaceProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_workspace_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceProfileRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceProfileRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_workspace_service_proto_rawDescGZIP(), []int{1}
}

var File_api_v1_workspace_service_proto protoreflect.FileDescriptor

const file_api_v1_workspace_service_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/v1/workspace_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\"y\n" +
	"\x10WorkspaceProfile\x12\x14\n" +
	"\x05owner\x18\x01 \x01(\tR\x05owner\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x12\n" +
	"\x04mode\x18\x03 \x01(\tR\x04mode\x12!\n" +
	"\finstance_url\x18\x06 \x01(\tR\vinstanceUrl\"\x1c\n" +
	"\x1aGetWorkspaceProfileRequest2\x97\x01\n" +
	"\x10WorkspaceService\x12\x82\x01\n" +
	"\x13GetWorkspaceProfile\x12(.memos.api.v1.GetWorkspaceProfileRequest\x1a\x1e.memos.api.v1.WorkspaceProfile\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/api/v1/workspace/profileB\xad\x01\n" +
	"\x10com.memos.api.v1B\x15WorkspaceServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_workspace_service_proto_rawDescOnce sync.Once
	file_api_v1_workspace_service_proto_rawDescData []byte
)

func file_api_v1_workspace_service_proto_rawDescGZIP() []byte {
	file_api_v1_workspace_service_proto_rawDescOnce.Do(func() {
		file_api_v1_workspace_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_workspace_service_proto_rawDesc), len(file_api_v1_workspace_service_proto_rawDesc)))
	})
	return file_api_v1_workspace_service_proto_rawDescData
}

var file_api_v1_workspace_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_v1_workspace_service_proto_goTypes = []any{
	(*WorkspaceProfile)(nil),           // 0: memos.api.v1.WorkspaceProfile
	(*GetWorkspaceProfileRequest)(nil), // 1: memos.api.v1.GetWorkspaceProfileRequest
}
var file_api_v1_workspace_service_proto_depIdxs = []int32{
	1, // 0: memos.api.v1.WorkspaceService.GetWorkspaceProfile:input_type -> memos.api.v1.GetWorkspaceProfileRequest
	0, // 1: memos.api.v1.WorkspaceService.GetWorkspaceProfile:output_type -> memos.api.v1.WorkspaceProfile
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_v1_workspace_service_proto_init() }
func file_api_v1_workspace_service_proto_init() {
	if File_api_v1_workspace_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_workspace_service_proto_rawDesc), len(file_api_v1_workspace_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_workspace_service_proto_goTypes,
		DependencyIndexes: file_api_v1_workspace_service_proto_depIdxs,
		MessageInfos:      file_api_v1_workspace_service_proto_msgTypes,
	}.Build()
	File_api_v1_workspace_service_proto = out.File
	file_api_v1_workspace_service_proto_goTypes = nil
	file_api_v1_workspace_service_proto_depIdxs = nil
}
