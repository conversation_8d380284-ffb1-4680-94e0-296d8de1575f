// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/user_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User_Role int32

const (
	User_ROLE_UNSPECIFIED User_Role = 0
	User_HOST             User_Role = 1
	User_ADMIN            User_Role = 2
	User_USER             User_Role = 3
)

// Enum value maps for User_Role.
var (
	User_Role_name = map[int32]string{
		0: "ROLE_UNSPECIFIED",
		1: "HOST",
		2: "ADMIN",
		3: "USER",
	}
	User_Role_value = map[string]int32{
		"ROLE_UNSPECIFIED": 0,
		"HOST":             1,
		"ADMIN":            2,
		"USER":             3,
	}
)

func (x User_Role) Enum() *User_Role {
	p := new(User_Role)
	*p = x
	return p
}

func (x User_Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Role) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1_user_service_proto_enumTypes[0].Descriptor()
}

func (User_Role) Type() protoreflect.EnumType {
	return &file_api_v1_user_service_proto_enumTypes[0]
}

func (x User_Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use User_Role.Descriptor instead.
func (User_Role) EnumDescriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{0, 0}
}

type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	// Format: users/{id}, id is the system generated auto-incremented id.
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Role          User_Role              `protobuf:"varint,3,opt,name=role,proto3,enum=memos.api.v1.User_Role" json:"role,omitempty"`
	Username      string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Nickname      string                 `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,7,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Description   string                 `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	Password      string                 `protobuf:"bytes,9,opt,name=password,proto3" json:"password,omitempty"`
	State         State                  `protobuf:"varint,10,opt,name=state,proto3,enum=memos.api.v1.State" json:"state,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_api_v1_user_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetRole() User_Role {
	if x != nil {
		return x.Role
	}
	return User_ROLE_UNSPECIFIED
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *User) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *User) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *User) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *User) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type ListUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersRequest) Reset() {
	*x = ListUsersRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersRequest) ProtoMessage() {}

func (x *ListUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersRequest.ProtoReflect.Descriptor instead.
func (*ListUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{1}
}

type ListUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*User                `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersResponse) Reset() {
	*x = ListUsersResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersResponse) ProtoMessage() {}

func (x *ListUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersResponse.ProtoReflect.Descriptor instead.
func (*ListUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListUsersResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetUserByUsernameRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The username of the user.
	Username      string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByUsernameRequest) Reset() {
	*x = GetUserByUsernameRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByUsernameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByUsernameRequest) ProtoMessage() {}

func (x *GetUserByUsernameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByUsernameRequest.ProtoReflect.Descriptor instead.
func (*GetUserByUsernameRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserByUsernameRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type GetUserAvatarBinaryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The raw HTTP body is bound to this field.
	HttpBody      *httpbody.HttpBody `protobuf:"bytes,2,opt,name=http_body,json=httpBody,proto3" json:"http_body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserAvatarBinaryRequest) Reset() {
	*x = GetUserAvatarBinaryRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAvatarBinaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAvatarBinaryRequest) ProtoMessage() {}

func (x *GetUserAvatarBinaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAvatarBinaryRequest.ProtoReflect.Descriptor instead.
func (*GetUserAvatarBinaryRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserAvatarBinaryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserAvatarBinaryRequest) GetHttpBody() *httpbody.HttpBody {
	if x != nil {
		return x.HttpBody
	}
	return nil
}

type CreateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateUserRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateUserRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateUserRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UserStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The timestamps when the memos were displayed.
	// We should return raw data to the client, and let the client format the data with the user's timezone.
	MemoDisplayTimestamps []*timestamppb.Timestamp `protobuf:"bytes,2,rep,name=memo_display_timestamps,json=memoDisplayTimestamps,proto3" json:"memo_display_timestamps,omitempty"`
	// The stats of memo types.
	MemoTypeStats *UserStats_MemoTypeStats `protobuf:"bytes,3,opt,name=memo_type_stats,json=memoTypeStats,proto3" json:"memo_type_stats,omitempty"`
	// The count of tags.
	// Format: "tag1": 1, "tag2": 2
	TagCount map[string]int32 `protobuf:"bytes,4,rep,name=tag_count,json=tagCount,proto3" json:"tag_count,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// The pinned memos of the user.
	PinnedMemos    []string `protobuf:"bytes,5,rep,name=pinned_memos,json=pinnedMemos,proto3" json:"pinned_memos,omitempty"`
	TotalMemoCount int32    `protobuf:"varint,6,opt,name=total_memo_count,json=totalMemoCount,proto3" json:"total_memo_count,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserStats) Reset() {
	*x = UserStats{}
	mi := &file_api_v1_user_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStats) ProtoMessage() {}

func (x *UserStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStats.ProtoReflect.Descriptor instead.
func (*UserStats) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{9}
}

func (x *UserStats) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserStats) GetMemoDisplayTimestamps() []*timestamppb.Timestamp {
	if x != nil {
		return x.MemoDisplayTimestamps
	}
	return nil
}

func (x *UserStats) GetMemoTypeStats() *UserStats_MemoTypeStats {
	if x != nil {
		return x.MemoTypeStats
	}
	return nil
}

func (x *UserStats) GetTagCount() map[string]int32 {
	if x != nil {
		return x.TagCount
	}
	return nil
}

func (x *UserStats) GetPinnedMemos() []string {
	if x != nil {
		return x.PinnedMemos
	}
	return nil
}

func (x *UserStats) GetTotalMemoCount() int32 {
	if x != nil {
		return x.TotalMemoCount
	}
	return 0
}

type ListAllUserStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllUserStatsRequest) Reset() {
	*x = ListAllUserStatsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllUserStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllUserStatsRequest) ProtoMessage() {}

func (x *ListAllUserStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllUserStatsRequest.ProtoReflect.Descriptor instead.
func (*ListAllUserStatsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{10}
}

type ListAllUserStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserStats     []*UserStats           `protobuf:"bytes,1,rep,name=user_stats,json=userStats,proto3" json:"user_stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllUserStatsResponse) Reset() {
	*x = ListAllUserStatsResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllUserStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllUserStatsResponse) ProtoMessage() {}

func (x *ListAllUserStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllUserStatsResponse.ProtoReflect.Descriptor instead.
func (*ListAllUserStatsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListAllUserStatsResponse) GetUserStats() []*UserStats {
	if x != nil {
		return x.UserStats
	}
	return nil
}

type GetUserStatsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatsRequest) Reset() {
	*x = GetUserStatsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsRequest) ProtoMessage() {}

func (x *GetUserStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsRequest.ProtoReflect.Descriptor instead.
func (*GetUserStatsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserStatsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UserSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The preferred locale of the user.
	Locale string `protobuf:"bytes,2,opt,name=locale,proto3" json:"locale,omitempty"`
	// The preferred appearance of the user.
	Appearance string `protobuf:"bytes,3,opt,name=appearance,proto3" json:"appearance,omitempty"`
	// The default visibility of the memo.
	MemoVisibility string `protobuf:"bytes,4,opt,name=memo_visibility,json=memoVisibility,proto3" json:"memo_visibility,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserSetting) Reset() {
	*x = UserSetting{}
	mi := &file_api_v1_user_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSetting) ProtoMessage() {}

func (x *UserSetting) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSetting.ProtoReflect.Descriptor instead.
func (*UserSetting) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{13}
}

func (x *UserSetting) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSetting) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *UserSetting) GetAppearance() string {
	if x != nil {
		return x.Appearance
	}
	return ""
}

func (x *UserSetting) GetMemoVisibility() string {
	if x != nil {
		return x.MemoVisibility
	}
	return ""
}

type GetUserSettingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSettingRequest) Reset() {
	*x = GetUserSettingRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSettingRequest) ProtoMessage() {}

func (x *GetUserSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSettingRequest.ProtoReflect.Descriptor instead.
func (*GetUserSettingRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserSettingRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UpdateUserSettingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Setting       *UserSetting           `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserSettingRequest) Reset() {
	*x = UpdateUserSettingRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserSettingRequest) ProtoMessage() {}

func (x *UpdateUserSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserSettingRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateUserSettingRequest) GetSetting() *UserSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateUserSettingRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type UserAccessToken struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	IssuedAt      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccessToken) Reset() {
	*x = UserAccessToken{}
	mi := &file_api_v1_user_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccessToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccessToken) ProtoMessage() {}

func (x *UserAccessToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccessToken.ProtoReflect.Descriptor instead.
func (*UserAccessToken) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{16}
}

func (x *UserAccessToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *UserAccessToken) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserAccessToken) GetIssuedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.IssuedAt
	}
	return nil
}

func (x *UserAccessToken) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type ListUserAccessTokensRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserAccessTokensRequest) Reset() {
	*x = ListUserAccessTokensRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserAccessTokensRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserAccessTokensRequest) ProtoMessage() {}

func (x *ListUserAccessTokensRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserAccessTokensRequest.ProtoReflect.Descriptor instead.
func (*ListUserAccessTokensRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListUserAccessTokensRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListUserAccessTokensResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessTokens  []*UserAccessToken     `protobuf:"bytes,1,rep,name=access_tokens,json=accessTokens,proto3" json:"access_tokens,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserAccessTokensResponse) Reset() {
	*x = ListUserAccessTokensResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserAccessTokensResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserAccessTokensResponse) ProtoMessage() {}

func (x *ListUserAccessTokensResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserAccessTokensResponse.ProtoReflect.Descriptor instead.
func (*ListUserAccessTokensResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{18}
}

func (x *ListUserAccessTokensResponse) GetAccessTokens() []*UserAccessToken {
	if x != nil {
		return x.AccessTokens
	}
	return nil
}

type CreateUserAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expires_at,json=expiresAt,proto3,oneof" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserAccessTokenRequest) Reset() {
	*x = CreateUserAccessTokenRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserAccessTokenRequest) ProtoMessage() {}

func (x *CreateUserAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*CreateUserAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{19}
}

func (x *CreateUserAccessTokenRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserAccessTokenRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateUserAccessTokenRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

type DeleteUserAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// access_token is the access token to delete.
	AccessToken   string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserAccessTokenRequest) Reset() {
	*x = DeleteUserAccessTokenRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserAccessTokenRequest) ProtoMessage() {}

func (x *DeleteUserAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteUserAccessTokenRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteUserAccessTokenRequest) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type Shortcut struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Filter        string                 `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Shortcut) Reset() {
	*x = Shortcut{}
	mi := &file_api_v1_user_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Shortcut) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Shortcut) ProtoMessage() {}

func (x *Shortcut) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Shortcut.ProtoReflect.Descriptor instead.
func (*Shortcut) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{21}
}

func (x *Shortcut) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Shortcut) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Shortcut) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

type ListShortcutsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Parent        string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShortcutsRequest) Reset() {
	*x = ListShortcutsRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShortcutsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShortcutsRequest) ProtoMessage() {}

func (x *ListShortcutsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShortcutsRequest.ProtoReflect.Descriptor instead.
func (*ListShortcutsRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListShortcutsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

type ListShortcutsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Shortcuts     []*Shortcut            `protobuf:"bytes,1,rep,name=shortcuts,proto3" json:"shortcuts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShortcutsResponse) Reset() {
	*x = ListShortcutsResponse{}
	mi := &file_api_v1_user_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShortcutsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShortcutsResponse) ProtoMessage() {}

func (x *ListShortcutsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShortcutsResponse.ProtoReflect.Descriptor instead.
func (*ListShortcutsResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListShortcutsResponse) GetShortcuts() []*Shortcut {
	if x != nil {
		return x.Shortcuts
	}
	return nil
}

type CreateShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Parent        string    `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	Shortcut      *Shortcut `protobuf:"bytes,2,opt,name=shortcut,proto3" json:"shortcut,omitempty"`
	ValidateOnly  bool      `protobuf:"varint,3,opt,name=validate_only,json=validateOnly,proto3" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateShortcutRequest) Reset() {
	*x = CreateShortcutRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateShortcutRequest) ProtoMessage() {}

func (x *CreateShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateShortcutRequest.ProtoReflect.Descriptor instead.
func (*CreateShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{24}
}

func (x *CreateShortcutRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateShortcutRequest) GetShortcut() *Shortcut {
	if x != nil {
		return x.Shortcut
	}
	return nil
}

func (x *CreateShortcutRequest) GetValidateOnly() bool {
	if x != nil {
		return x.ValidateOnly
	}
	return false
}

type UpdateShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Parent        string                 `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	Shortcut      *Shortcut              `protobuf:"bytes,2,opt,name=shortcut,proto3" json:"shortcut,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateShortcutRequest) Reset() {
	*x = UpdateShortcutRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateShortcutRequest) ProtoMessage() {}

func (x *UpdateShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateShortcutRequest.ProtoReflect.Descriptor instead.
func (*UpdateShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateShortcutRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *UpdateShortcutRequest) GetShortcut() *Shortcut {
	if x != nil {
		return x.Shortcut
	}
	return nil
}

func (x *UpdateShortcutRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteShortcutRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the user.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// The id of the shortcut.
	Id            string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteShortcutRequest) Reset() {
	*x = DeleteShortcutRequest{}
	mi := &file_api_v1_user_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteShortcutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteShortcutRequest) ProtoMessage() {}

func (x *DeleteShortcutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteShortcutRequest.ProtoReflect.Descriptor instead.
func (*DeleteShortcutRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteShortcutRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *DeleteShortcutRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UserStats_MemoTypeStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LinkCount     int32                  `protobuf:"varint,1,opt,name=link_count,json=linkCount,proto3" json:"link_count,omitempty"`
	CodeCount     int32                  `protobuf:"varint,2,opt,name=code_count,json=codeCount,proto3" json:"code_count,omitempty"`
	TodoCount     int32                  `protobuf:"varint,3,opt,name=todo_count,json=todoCount,proto3" json:"todo_count,omitempty"`
	UndoCount     int32                  `protobuf:"varint,4,opt,name=undo_count,json=undoCount,proto3" json:"undo_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStats_MemoTypeStats) Reset() {
	*x = UserStats_MemoTypeStats{}
	mi := &file_api_v1_user_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStats_MemoTypeStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStats_MemoTypeStats) ProtoMessage() {}

func (x *UserStats_MemoTypeStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_user_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStats_MemoTypeStats.ProtoReflect.Descriptor instead.
func (*UserStats_MemoTypeStats) Descriptor() ([]byte, []int) {
	return file_api_v1_user_service_proto_rawDescGZIP(), []int{9, 1}
}

func (x *UserStats_MemoTypeStats) GetLinkCount() int32 {
	if x != nil {
		return x.LinkCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetCodeCount() int32 {
	if x != nil {
		return x.CodeCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetTodoCount() int32 {
	if x != nil {
		return x.TodoCount
	}
	return 0
}

func (x *UserStats_MemoTypeStats) GetUndoCount() int32 {
	if x != nil {
		return x.UndoCount
	}
	return 0
}

var File_api_v1_user_service_proto protoreflect.FileDescriptor

const file_api_v1_user_service_proto_rawDesc = "" +
	"\n" +
	"\x19api/v1/user_service.proto\x12\fmemos.api.v1\x1a\x13api/v1/common.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/httpbody.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xeb\x03\n" +
	"\x04User\x12\x1a\n" +
	"\x04name\x18\x01 \x01(\tB\x06\xe0A\x03\xe0A\bR\x04name\x12+\n" +
	"\x04role\x18\x03 \x01(\x0e2\x17.memos.api.v1.User.RoleR\x04role\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\x06 \x01(\tR\bnickname\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\a \x01(\tR\tavatarUrl\x12 \n" +
	"\vdescription\x18\b \x01(\tR\vdescription\x12\x1f\n" +
	"\bpassword\x18\t \x01(\tB\x03\xe0A\x04R\bpassword\x12)\n" +
	"\x05state\x18\n" +
	" \x01(\x0e2\x13.memos.api.v1.StateR\x05state\x12@\n" +
	"\vcreate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\";\n" +
	"\x04Role\x12\x14\n" +
	"\x10ROLE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04HOST\x10\x01\x12\t\n" +
	"\x05ADMIN\x10\x02\x12\b\n" +
	"\x04USER\x10\x03\"\x12\n" +
	"\x10ListUsersRequest\"=\n" +
	"\x11ListUsersResponse\x12(\n" +
	"\x05users\x18\x01 \x03(\v2\x12.memos.api.v1.UserR\x05users\"$\n" +
	"\x0eGetUserRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"6\n" +
	"\x18GetUserByUsernameRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\"c\n" +
	"\x1aGetUserAvatarBinaryRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x121\n" +
	"\thttp_body\x18\x02 \x01(\v2\x14.google.api.HttpBodyR\bhttpBody\";\n" +
	"\x11CreateUserRequest\x12&\n" +
	"\x04user\x18\x01 \x01(\v2\x12.memos.api.v1.UserR\x04user\"}\n" +
	"\x11UpdateUserRequest\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x12.memos.api.v1.UserB\x03\xe0A\x02R\x04user\x12;\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"'\n" +
	"\x11DeleteUserRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x9e\x04\n" +
	"\tUserStats\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12R\n" +
	"\x17memo_display_timestamps\x18\x02 \x03(\v2\x1a.google.protobuf.TimestampR\x15memoDisplayTimestamps\x12M\n" +
	"\x0fmemo_type_stats\x18\x03 \x01(\v2%.memos.api.v1.UserStats.MemoTypeStatsR\rmemoTypeStats\x12B\n" +
	"\ttag_count\x18\x04 \x03(\v2%.memos.api.v1.UserStats.TagCountEntryR\btagCount\x12!\n" +
	"\fpinned_memos\x18\x05 \x03(\tR\vpinnedMemos\x12(\n" +
	"\x10total_memo_count\x18\x06 \x01(\x05R\x0etotalMemoCount\x1a;\n" +
	"\rTagCountEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a\x8b\x01\n" +
	"\rMemoTypeStats\x12\x1d\n" +
	"\n" +
	"link_count\x18\x01 \x01(\x05R\tlinkCount\x12\x1d\n" +
	"\n" +
	"code_count\x18\x02 \x01(\x05R\tcodeCount\x12\x1d\n" +
	"\n" +
	"todo_count\x18\x03 \x01(\x05R\ttodoCount\x12\x1d\n" +
	"\n" +
	"undo_count\x18\x04 \x01(\x05R\tundoCount\"\x19\n" +
	"\x17ListAllUserStatsRequest\"R\n" +
	"\x18ListAllUserStatsResponse\x126\n" +
	"\n" +
	"user_stats\x18\x01 \x03(\v2\x17.memos.api.v1.UserStatsR\tuserStats\")\n" +
	"\x13GetUserStatsRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x82\x01\n" +
	"\vUserSetting\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06locale\x18\x02 \x01(\tR\x06locale\x12\x1e\n" +
	"\n" +
	"appearance\x18\x03 \x01(\tR\n" +
	"appearance\x12'\n" +
	"\x0fmemo_visibility\x18\x04 \x01(\tR\x0ememoVisibility\"+\n" +
	"\x15GetUserSettingRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x91\x01\n" +
	"\x18UpdateUserSettingRequest\x128\n" +
	"\asetting\x18\x01 \x01(\v2\x19.memos.api.v1.UserSettingB\x03\xe0A\x02R\asetting\x12;\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"\xca\x01\n" +
	"\x0fUserAccessToken\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x127\n" +
	"\tissued_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\bissuedAt\x129\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\"1\n" +
	"\x1bListUserAccessTokensRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"b\n" +
	"\x1cListUserAccessTokensResponse\x12B\n" +
	"\raccess_tokens\x18\x01 \x03(\v2\x1d.memos.api.v1.UserAccessTokenR\faccessTokens\"\xa3\x01\n" +
	"\x1cCreateUserAccessTokenRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12>\n" +
	"\n" +
	"expires_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\texpiresAt\x88\x01\x01B\r\n" +
	"\v_expires_at\"U\n" +
	"\x1cDeleteUserAccessTokenRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12!\n" +
	"\faccess_token\x18\x02 \x01(\tR\vaccessToken\"H\n" +
	"\bShortcut\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x16\n" +
	"\x06filter\x18\x03 \x01(\tR\x06filter\".\n" +
	"\x14ListShortcutsRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\"M\n" +
	"\x15ListShortcutsResponse\x124\n" +
	"\tshortcuts\x18\x01 \x03(\v2\x16.memos.api.v1.ShortcutR\tshortcuts\"\x88\x01\n" +
	"\x15CreateShortcutRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x122\n" +
	"\bshortcut\x18\x02 \x01(\v2\x16.memos.api.v1.ShortcutR\bshortcut\x12#\n" +
	"\rvalidate_only\x18\x03 \x01(\bR\fvalidateOnly\"\xa0\x01\n" +
	"\x15UpdateShortcutRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x122\n" +
	"\bshortcut\x18\x02 \x01(\v2\x16.memos.api.v1.ShortcutR\bshortcut\x12;\n" +
	"\vupdate_mask\x18\x03 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"?\n" +
	"\x15DeleteShortcutRequest\x12\x16\n" +
	"\x06parent\x18\x01 \x01(\tR\x06parent\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id2\xa9\x13\n" +
	"\vUserService\x12c\n" +
	"\tListUsers\x12\x1e.memos.api.v1.ListUsersRequest\x1a\x1f.memos.api.v1.ListUsersResponse\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/api/v1/users\x12b\n" +
	"\aGetUser\x12\x1c.memos.api.v1.GetUserRequest\x1a\x12.memos.api.v1.User\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/{name=users/*}\x12z\n" +
	"\x11GetUserByUsername\x12&.memos.api.v1.GetUserByUsernameRequest\x1a\x12.memos.api.v1.User\")\xdaA\busername\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/users:username\x12\x81\x01\n" +
	"\x13GetUserAvatarBinary\x12(.memos.api.v1.GetUserAvatarBinaryRequest\x1a\x14.google.api.HttpBody\"*\xdaA\x04name\x82\xd3\xe4\x93\x02\x1d\x12\x1b/file/{name=users/*}/avatar\x12e\n" +
	"\n" +
	"CreateUser\x12\x1f.memos.api.v1.CreateUserRequest\x1a\x12.memos.api.v1.User\"\"\xdaA\x04user\x82\xd3\xe4\x93\x02\x15:\x04user\"\r/api/v1/users\x12\x7f\n" +
	"\n" +
	"UpdateUser\x12\x1f.memos.api.v1.UpdateUserRequest\x1a\x12.memos.api.v1.User\"<\xdaA\x10user,update_mask\x82\xd3\xe4\x93\x02#:\x04user2\x1b/api/v1/{user.name=users/*}\x12l\n" +
	"\n" +
	"DeleteUser\x12\x1f.memos.api.v1.DeleteUserRequest\x1a\x16.google.protobuf.Empty\"%\xdaA\x04name\x82\xd3\xe4\x93\x02\x18*\x16/api/v1/{name=users/*}\x12\x80\x01\n" +
	"\x10ListAllUserStats\x12%.memos.api.v1.ListAllUserStatsRequest\x1a&.memos.api.v1.ListAllUserStatsResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\"\x15/api/v1/users/-/stats\x12w\n" +
	"\fGetUserStats\x12!.memos.api.v1.GetUserStatsRequest\x1a\x17.memos.api.v1.UserStats\"+\xdaA\x04name\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/{name=users/*}/stats\x12\x7f\n" +
	"\x0eGetUserSetting\x12#.memos.api.v1.GetUserSettingRequest\x1a\x19.memos.api.v1.UserSetting\"-\xdaA\x04name\x82\xd3\xe4\x93\x02 \x12\x1e/api/v1/{name=users/*}/setting\x12\xa5\x01\n" +
	"\x11UpdateUserSetting\x12&.memos.api.v1.UpdateUserSettingRequest\x1a\x19.memos.api.v1.UserSetting\"M\xdaA\x13setting,update_mask\x82\xd3\xe4\x93\x021:\asetting2&/api/v1/{setting.name=users/*/setting}\x12\xa2\x01\n" +
	"\x14ListUserAccessTokens\x12).memos.api.v1.ListUserAccessTokensRequest\x1a*.memos.api.v1.ListUserAccessTokensResponse\"3\xdaA\x04name\x82\xd3\xe4\x93\x02&\x12$/api/v1/{name=users/*}/access_tokens\x12\x9a\x01\n" +
	"\x15CreateUserAccessToken\x12*.memos.api.v1.CreateUserAccessTokenRequest\x1a\x1d.memos.api.v1.UserAccessToken\"6\xdaA\x04name\x82\xd3\xe4\x93\x02):\x01*\"$/api/v1/{name=users/*}/access_tokens\x12\xac\x01\n" +
	"\x15DeleteUserAccessToken\x12*.memos.api.v1.DeleteUserAccessTokenRequest\x1a\x16.google.protobuf.Empty\"O\xdaA\x11name,access_token\x82\xd3\xe4\x93\x025*3/api/v1/{name=users/*}/access_tokens/{access_token}\x12\x8d\x01\n" +
	"\rListShortcuts\x12\".memos.api.v1.ListShortcutsRequest\x1a#.memos.api.v1.ListShortcutsResponse\"3\xdaA\x06parent\x82\xd3\xe4\x93\x02$\x12\"/api/v1/{parent=users/*}/shortcuts\x12\x95\x01\n" +
	"\x0eCreateShortcut\x12#.memos.api.v1.CreateShortcutRequest\x1a\x16.memos.api.v1.Shortcut\"F\xdaA\x0fparent,shortcut\x82\xd3\xe4\x93\x02.:\bshortcut\"\"/api/v1/{parent=users/*}/shortcuts\x12\xaf\x01\n" +
	"\x0eUpdateShortcut\x12#.memos.api.v1.UpdateShortcutRequest\x1a\x16.memos.api.v1.Shortcut\"`\xdaA\x1bparent,shortcut,update_mask\x82\xd3\xe4\x93\x02<:\bshortcut20/api/v1/{parent=users/*}/shortcuts/{shortcut.id}\x12\x8a\x01\n" +
	"\x0eDeleteShortcut\x12#.memos.api.v1.DeleteShortcutRequest\x1a\x16.google.protobuf.Empty\";\xdaA\tparent,id\x82\xd3\xe4\x93\x02)*'/api/v1/{parent=users/*}/shortcuts/{id}B\xa8\x01\n" +
	"\x10com.memos.api.v1B\x10UserServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_user_service_proto_rawDescOnce sync.Once
	file_api_v1_user_service_proto_rawDescData []byte
)

func file_api_v1_user_service_proto_rawDescGZIP() []byte {
	file_api_v1_user_service_proto_rawDescOnce.Do(func() {
		file_api_v1_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_user_service_proto_rawDesc), len(file_api_v1_user_service_proto_rawDesc)))
	})
	return file_api_v1_user_service_proto_rawDescData
}

var file_api_v1_user_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_v1_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_v1_user_service_proto_goTypes = []any{
	(User_Role)(0),                       // 0: memos.api.v1.User.Role
	(*User)(nil),                         // 1: memos.api.v1.User
	(*ListUsersRequest)(nil),             // 2: memos.api.v1.ListUsersRequest
	(*ListUsersResponse)(nil),            // 3: memos.api.v1.ListUsersResponse
	(*GetUserRequest)(nil),               // 4: memos.api.v1.GetUserRequest
	(*GetUserByUsernameRequest)(nil),     // 5: memos.api.v1.GetUserByUsernameRequest
	(*GetUserAvatarBinaryRequest)(nil),   // 6: memos.api.v1.GetUserAvatarBinaryRequest
	(*CreateUserRequest)(nil),            // 7: memos.api.v1.CreateUserRequest
	(*UpdateUserRequest)(nil),            // 8: memos.api.v1.UpdateUserRequest
	(*DeleteUserRequest)(nil),            // 9: memos.api.v1.DeleteUserRequest
	(*UserStats)(nil),                    // 10: memos.api.v1.UserStats
	(*ListAllUserStatsRequest)(nil),      // 11: memos.api.v1.ListAllUserStatsRequest
	(*ListAllUserStatsResponse)(nil),     // 12: memos.api.v1.ListAllUserStatsResponse
	(*GetUserStatsRequest)(nil),          // 13: memos.api.v1.GetUserStatsRequest
	(*UserSetting)(nil),                  // 14: memos.api.v1.UserSetting
	(*GetUserSettingRequest)(nil),        // 15: memos.api.v1.GetUserSettingRequest
	(*UpdateUserSettingRequest)(nil),     // 16: memos.api.v1.UpdateUserSettingRequest
	(*UserAccessToken)(nil),              // 17: memos.api.v1.UserAccessToken
	(*ListUserAccessTokensRequest)(nil),  // 18: memos.api.v1.ListUserAccessTokensRequest
	(*ListUserAccessTokensResponse)(nil), // 19: memos.api.v1.ListUserAccessTokensResponse
	(*CreateUserAccessTokenRequest)(nil), // 20: memos.api.v1.CreateUserAccessTokenRequest
	(*DeleteUserAccessTokenRequest)(nil), // 21: memos.api.v1.DeleteUserAccessTokenRequest
	(*Shortcut)(nil),                     // 22: memos.api.v1.Shortcut
	(*ListShortcutsRequest)(nil),         // 23: memos.api.v1.ListShortcutsRequest
	(*ListShortcutsResponse)(nil),        // 24: memos.api.v1.ListShortcutsResponse
	(*CreateShortcutRequest)(nil),        // 25: memos.api.v1.CreateShortcutRequest
	(*UpdateShortcutRequest)(nil),        // 26: memos.api.v1.UpdateShortcutRequest
	(*DeleteShortcutRequest)(nil),        // 27: memos.api.v1.DeleteShortcutRequest
	nil,                                  // 28: memos.api.v1.UserStats.TagCountEntry
	(*UserStats_MemoTypeStats)(nil),      // 29: memos.api.v1.UserStats.MemoTypeStats
	(State)(0),                           // 30: memos.api.v1.State
	(*timestamppb.Timestamp)(nil),        // 31: google.protobuf.Timestamp
	(*httpbody.HttpBody)(nil),            // 32: google.api.HttpBody
	(*fieldmaskpb.FieldMask)(nil),        // 33: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),                // 34: google.protobuf.Empty
}
var file_api_v1_user_service_proto_depIdxs = []int32{
	0,  // 0: memos.api.v1.User.role:type_name -> memos.api.v1.User.Role
	30, // 1: memos.api.v1.User.state:type_name -> memos.api.v1.State
	31, // 2: memos.api.v1.User.create_time:type_name -> google.protobuf.Timestamp
	31, // 3: memos.api.v1.User.update_time:type_name -> google.protobuf.Timestamp
	1,  // 4: memos.api.v1.ListUsersResponse.users:type_name -> memos.api.v1.User
	32, // 5: memos.api.v1.GetUserAvatarBinaryRequest.http_body:type_name -> google.api.HttpBody
	1,  // 6: memos.api.v1.CreateUserRequest.user:type_name -> memos.api.v1.User
	1,  // 7: memos.api.v1.UpdateUserRequest.user:type_name -> memos.api.v1.User
	33, // 8: memos.api.v1.UpdateUserRequest.update_mask:type_name -> google.protobuf.FieldMask
	31, // 9: memos.api.v1.UserStats.memo_display_timestamps:type_name -> google.protobuf.Timestamp
	29, // 10: memos.api.v1.UserStats.memo_type_stats:type_name -> memos.api.v1.UserStats.MemoTypeStats
	28, // 11: memos.api.v1.UserStats.tag_count:type_name -> memos.api.v1.UserStats.TagCountEntry
	10, // 12: memos.api.v1.ListAllUserStatsResponse.user_stats:type_name -> memos.api.v1.UserStats
	14, // 13: memos.api.v1.UpdateUserSettingRequest.setting:type_name -> memos.api.v1.UserSetting
	33, // 14: memos.api.v1.UpdateUserSettingRequest.update_mask:type_name -> google.protobuf.FieldMask
	31, // 15: memos.api.v1.UserAccessToken.issued_at:type_name -> google.protobuf.Timestamp
	31, // 16: memos.api.v1.UserAccessToken.expires_at:type_name -> google.protobuf.Timestamp
	17, // 17: memos.api.v1.ListUserAccessTokensResponse.access_tokens:type_name -> memos.api.v1.UserAccessToken
	31, // 18: memos.api.v1.CreateUserAccessTokenRequest.expires_at:type_name -> google.protobuf.Timestamp
	22, // 19: memos.api.v1.ListShortcutsResponse.shortcuts:type_name -> memos.api.v1.Shortcut
	22, // 20: memos.api.v1.CreateShortcutRequest.shortcut:type_name -> memos.api.v1.Shortcut
	22, // 21: memos.api.v1.UpdateShortcutRequest.shortcut:type_name -> memos.api.v1.Shortcut
	33, // 22: memos.api.v1.UpdateShortcutRequest.update_mask:type_name -> google.protobuf.FieldMask
	2,  // 23: memos.api.v1.UserService.ListUsers:input_type -> memos.api.v1.ListUsersRequest
	4,  // 24: memos.api.v1.UserService.GetUser:input_type -> memos.api.v1.GetUserRequest
	5,  // 25: memos.api.v1.UserService.GetUserByUsername:input_type -> memos.api.v1.GetUserByUsernameRequest
	6,  // 26: memos.api.v1.UserService.GetUserAvatarBinary:input_type -> memos.api.v1.GetUserAvatarBinaryRequest
	7,  // 27: memos.api.v1.UserService.CreateUser:input_type -> memos.api.v1.CreateUserRequest
	8,  // 28: memos.api.v1.UserService.UpdateUser:input_type -> memos.api.v1.UpdateUserRequest
	9,  // 29: memos.api.v1.UserService.DeleteUser:input_type -> memos.api.v1.DeleteUserRequest
	11, // 30: memos.api.v1.UserService.ListAllUserStats:input_type -> memos.api.v1.ListAllUserStatsRequest
	13, // 31: memos.api.v1.UserService.GetUserStats:input_type -> memos.api.v1.GetUserStatsRequest
	15, // 32: memos.api.v1.UserService.GetUserSetting:input_type -> memos.api.v1.GetUserSettingRequest
	16, // 33: memos.api.v1.UserService.UpdateUserSetting:input_type -> memos.api.v1.UpdateUserSettingRequest
	18, // 34: memos.api.v1.UserService.ListUserAccessTokens:input_type -> memos.api.v1.ListUserAccessTokensRequest
	20, // 35: memos.api.v1.UserService.CreateUserAccessToken:input_type -> memos.api.v1.CreateUserAccessTokenRequest
	21, // 36: memos.api.v1.UserService.DeleteUserAccessToken:input_type -> memos.api.v1.DeleteUserAccessTokenRequest
	23, // 37: memos.api.v1.UserService.ListShortcuts:input_type -> memos.api.v1.ListShortcutsRequest
	25, // 38: memos.api.v1.UserService.CreateShortcut:input_type -> memos.api.v1.CreateShortcutRequest
	26, // 39: memos.api.v1.UserService.UpdateShortcut:input_type -> memos.api.v1.UpdateShortcutRequest
	27, // 40: memos.api.v1.UserService.DeleteShortcut:input_type -> memos.api.v1.DeleteShortcutRequest
	3,  // 41: memos.api.v1.UserService.ListUsers:output_type -> memos.api.v1.ListUsersResponse
	1,  // 42: memos.api.v1.UserService.GetUser:output_type -> memos.api.v1.User
	1,  // 43: memos.api.v1.UserService.GetUserByUsername:output_type -> memos.api.v1.User
	32, // 44: memos.api.v1.UserService.GetUserAvatarBinary:output_type -> google.api.HttpBody
	1,  // 45: memos.api.v1.UserService.CreateUser:output_type -> memos.api.v1.User
	1,  // 46: memos.api.v1.UserService.UpdateUser:output_type -> memos.api.v1.User
	34, // 47: memos.api.v1.UserService.DeleteUser:output_type -> google.protobuf.Empty
	12, // 48: memos.api.v1.UserService.ListAllUserStats:output_type -> memos.api.v1.ListAllUserStatsResponse
	10, // 49: memos.api.v1.UserService.GetUserStats:output_type -> memos.api.v1.UserStats
	14, // 50: memos.api.v1.UserService.GetUserSetting:output_type -> memos.api.v1.UserSetting
	14, // 51: memos.api.v1.UserService.UpdateUserSetting:output_type -> memos.api.v1.UserSetting
	19, // 52: memos.api.v1.UserService.ListUserAccessTokens:output_type -> memos.api.v1.ListUserAccessTokensResponse
	17, // 53: memos.api.v1.UserService.CreateUserAccessToken:output_type -> memos.api.v1.UserAccessToken
	34, // 54: memos.api.v1.UserService.DeleteUserAccessToken:output_type -> google.protobuf.Empty
	24, // 55: memos.api.v1.UserService.ListShortcuts:output_type -> memos.api.v1.ListShortcutsResponse
	22, // 56: memos.api.v1.UserService.CreateShortcut:output_type -> memos.api.v1.Shortcut
	22, // 57: memos.api.v1.UserService.UpdateShortcut:output_type -> memos.api.v1.Shortcut
	34, // 58: memos.api.v1.UserService.DeleteShortcut:output_type -> google.protobuf.Empty
	41, // [41:59] is the sub-list for method output_type
	23, // [23:41] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_v1_user_service_proto_init() }
func file_api_v1_user_service_proto_init() {
	if File_api_v1_user_service_proto != nil {
		return
	}
	file_api_v1_common_proto_init()
	file_api_v1_user_service_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_user_service_proto_rawDesc), len(file_api_v1_user_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_user_service_proto_goTypes,
		DependencyIndexes: file_api_v1_user_service_proto_depIdxs,
		EnumInfos:         file_api_v1_user_service_proto_enumTypes,
		MessageInfos:      file_api_v1_user_service_proto_msgTypes,
	}.Build()
	File_api_v1_user_service_proto = out.File
	file_api_v1_user_service_proto_goTypes = nil
	file_api_v1_user_service_proto_depIdxs = nil
}
