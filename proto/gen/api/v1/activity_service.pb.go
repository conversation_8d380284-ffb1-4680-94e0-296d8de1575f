// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/activity_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Activity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the activity.
	// Format: activities/{id}
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The name of the creator.
	// Format: users/{user}
	Creator string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	// The type of the activity.
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// The level of the activity.
	Level string `protobuf:"bytes,4,opt,name=level,proto3" json:"level,omitempty"`
	// The create time of the activity.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The payload of the activity.
	Payload       *ActivityPayload `protobuf:"bytes,6,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Activity) Reset() {
	*x = Activity{}
	mi := &file_api_v1_activity_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Activity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity) ProtoMessage() {}

func (x *Activity) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity.ProtoReflect.Descriptor instead.
func (*Activity) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{0}
}

func (x *Activity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Activity) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Activity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Activity) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Activity) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Activity) GetPayload() *ActivityPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type ActivityPayload struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	MemoComment   *ActivityMemoCommentPayload `protobuf:"bytes,1,opt,name=memo_comment,json=memoComment,proto3" json:"memo_comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityPayload) Reset() {
	*x = ActivityPayload{}
	mi := &file_api_v1_activity_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityPayload) ProtoMessage() {}

func (x *ActivityPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityPayload.ProtoReflect.Descriptor instead.
func (*ActivityPayload) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{1}
}

func (x *ActivityPayload) GetMemoComment() *ActivityMemoCommentPayload {
	if x != nil {
		return x.MemoComment
	}
	return nil
}

// ActivityMemoCommentPayload represents the payload of a memo comment activity.
type ActivityMemoCommentPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The memo name of comment.
	// Refer to `Memo.name`.
	Memo string `protobuf:"bytes,1,opt,name=memo,proto3" json:"memo,omitempty"`
	// The name of related memo.
	RelatedMemo   string `protobuf:"bytes,2,opt,name=related_memo,json=relatedMemo,proto3" json:"related_memo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityMemoCommentPayload) Reset() {
	*x = ActivityMemoCommentPayload{}
	mi := &file_api_v1_activity_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityMemoCommentPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityMemoCommentPayload) ProtoMessage() {}

func (x *ActivityMemoCommentPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityMemoCommentPayload.ProtoReflect.Descriptor instead.
func (*ActivityMemoCommentPayload) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{2}
}

func (x *ActivityMemoCommentPayload) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *ActivityMemoCommentPayload) GetRelatedMemo() string {
	if x != nil {
		return x.RelatedMemo
	}
	return ""
}

type GetActivityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the activity.
	// Format: activities/{id}, id is the system generated auto-incremented id.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActivityRequest) Reset() {
	*x = GetActivityRequest{}
	mi := &file_api_v1_activity_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityRequest) ProtoMessage() {}

func (x *GetActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_activity_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityRequest.ProtoReflect.Descriptor instead.
func (*GetActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_activity_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetActivityRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_api_v1_activity_service_proto protoreflect.FileDescriptor

const file_api_v1_activity_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/v1/activity_service.proto\x12\fmemos.api.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe5\x01\n" +
	"\bActivity\x12\x1a\n" +
	"\x04name\x18\x01 \x01(\tB\x06\xe0A\x03\xe0A\bR\x04name\x12\x18\n" +
	"\acreator\x18\x02 \x01(\tR\acreator\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x14\n" +
	"\x05level\x18\x04 \x01(\tR\x05level\x12@\n" +
	"\vcreate_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x127\n" +
	"\apayload\x18\x06 \x01(\v2\x1d.memos.api.v1.ActivityPayloadR\apayload\"^\n" +
	"\x0fActivityPayload\x12K\n" +
	"\fmemo_comment\x18\x01 \x01(\v2(.memos.api.v1.ActivityMemoCommentPayloadR\vmemoComment\"S\n" +
	"\x1aActivityMemoCommentPayload\x12\x12\n" +
	"\x04memo\x18\x01 \x01(\tR\x04memo\x12!\n" +
	"\frelated_memo\x18\x02 \x01(\tR\vrelatedMemo\"(\n" +
	"\x12GetActivityRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name2\x86\x01\n" +
	"\x0fActivityService\x12s\n" +
	"\vGetActivity\x12 .memos.api.v1.GetActivityRequest\x1a\x16.memos.api.v1.Activity\"*\xdaA\x04name\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v1/{name=activities/*}B\xac\x01\n" +
	"\x10com.memos.api.v1B\x14ActivityServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_activity_service_proto_rawDescOnce sync.Once
	file_api_v1_activity_service_proto_rawDescData []byte
)

func file_api_v1_activity_service_proto_rawDescGZIP() []byte {
	file_api_v1_activity_service_proto_rawDescOnce.Do(func() {
		file_api_v1_activity_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_activity_service_proto_rawDesc), len(file_api_v1_activity_service_proto_rawDesc)))
	})
	return file_api_v1_activity_service_proto_rawDescData
}

var file_api_v1_activity_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_v1_activity_service_proto_goTypes = []any{
	(*Activity)(nil),                   // 0: memos.api.v1.Activity
	(*ActivityPayload)(nil),            // 1: memos.api.v1.ActivityPayload
	(*ActivityMemoCommentPayload)(nil), // 2: memos.api.v1.ActivityMemoCommentPayload
	(*GetActivityRequest)(nil),         // 3: memos.api.v1.GetActivityRequest
	(*timestamppb.Timestamp)(nil),      // 4: google.protobuf.Timestamp
}
var file_api_v1_activity_service_proto_depIdxs = []int32{
	4, // 0: memos.api.v1.Activity.create_time:type_name -> google.protobuf.Timestamp
	1, // 1: memos.api.v1.Activity.payload:type_name -> memos.api.v1.ActivityPayload
	2, // 2: memos.api.v1.ActivityPayload.memo_comment:type_name -> memos.api.v1.ActivityMemoCommentPayload
	3, // 3: memos.api.v1.ActivityService.GetActivity:input_type -> memos.api.v1.GetActivityRequest
	0, // 4: memos.api.v1.ActivityService.GetActivity:output_type -> memos.api.v1.Activity
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_v1_activity_service_proto_init() }
func file_api_v1_activity_service_proto_init() {
	if File_api_v1_activity_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_activity_service_proto_rawDesc), len(file_api_v1_activity_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_activity_service_proto_goTypes,
		DependencyIndexes: file_api_v1_activity_service_proto_depIdxs,
		MessageInfos:      file_api_v1_activity_service_proto_msgTypes,
	}.Build()
	File_api_v1_activity_service_proto = out.File
	file_api_v1_activity_service_proto_goTypes = nil
	file_api_v1_activity_service_proto_depIdxs = nil
}
