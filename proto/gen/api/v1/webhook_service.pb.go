// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/v1/webhook_service.proto

package apiv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Webhook struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the creator.
	Creator       string                 `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Url           string                 `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Webhook) Reset() {
	*x = Webhook{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Webhook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Webhook) ProtoMessage() {}

func (x *Webhook) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Webhook.ProtoReflect.Descriptor instead.
func (*Webhook) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{0}
}

func (x *Webhook) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Webhook) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Webhook) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Webhook) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Webhook) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Webhook) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CreateWebhookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWebhookRequest) Reset() {
	*x = CreateWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWebhookRequest) ProtoMessage() {}

func (x *CreateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWebhookRequest.ProtoReflect.Descriptor instead.
func (*CreateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateWebhookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWebhookRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetWebhookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWebhookRequest) Reset() {
	*x = GetWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookRequest) ProtoMessage() {}

func (x *GetWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetWebhookRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListWebhooksRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the creator.
	Creator       string `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWebhooksRequest) Reset() {
	*x = ListWebhooksRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWebhooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksRequest) ProtoMessage() {}

func (x *ListWebhooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListWebhooksRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type ListWebhooksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Webhooks      []*Webhook             `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWebhooksResponse) Reset() {
	*x = ListWebhooksResponse{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWebhooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksResponse) ProtoMessage() {}

func (x *ListWebhooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksResponse) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListWebhooksResponse) GetWebhooks() []*Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

type UpdateWebhookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Webhook       *Webhook               `protobuf:"bytes,1,opt,name=webhook,proto3" json:"webhook,omitempty"`
	UpdateMask    *fieldmaskpb.FieldMask `protobuf:"bytes,2,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWebhookRequest) Reset() {
	*x = UpdateWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookRequest) ProtoMessage() {}

func (x *UpdateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookRequest.ProtoReflect.Descriptor instead.
func (*UpdateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWebhookRequest) GetWebhook() *Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

func (x *UpdateWebhookRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type DeleteWebhookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWebhookRequest) Reset() {
	*x = DeleteWebhookRequest{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWebhookRequest) ProtoMessage() {}

func (x *DeleteWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWebhookRequest.ProtoReflect.Descriptor instead.
func (*DeleteWebhookRequest) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteWebhookRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type WebhookRequestPayload struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Url          string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	ActivityType string                 `protobuf:"bytes,2,opt,name=activity_type,json=activityType,proto3" json:"activity_type,omitempty"`
	// The name of the creator.
	// Format: users/{user}
	Creator       string                 `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Memo          *Memo                  `protobuf:"bytes,5,opt,name=memo,proto3" json:"memo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhookRequestPayload) Reset() {
	*x = WebhookRequestPayload{}
	mi := &file_api_v1_webhook_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookRequestPayload) ProtoMessage() {}

func (x *WebhookRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1_webhook_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookRequestPayload.ProtoReflect.Descriptor instead.
func (*WebhookRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_v1_webhook_service_proto_rawDescGZIP(), []int{7}
}

func (x *WebhookRequestPayload) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *WebhookRequestPayload) GetActivityType() string {
	if x != nil {
		return x.ActivityType
	}
	return ""
}

func (x *WebhookRequestPayload) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *WebhookRequestPayload) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *WebhookRequestPayload) GetMemo() *Memo {
	if x != nil {
		return x.Memo
	}
	return nil
}

var File_api_v1_webhook_service_proto protoreflect.FileDescriptor

const file_api_v1_webhook_service_proto_rawDesc = "" +
	"\n" +
	"\x1capi/v1/webhook_service.proto\x12\fmemos.api.v1\x1a\x19api/v1/memo_service.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd3\x01\n" +
	"\aWebhook\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x18\n" +
	"\acreator\x18\x02 \x01(\tR\acreator\x12;\n" +
	"\vcreate_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x10\n" +
	"\x03url\x18\x06 \x01(\tR\x03url\"<\n" +
	"\x14CreateWebhookRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"#\n" +
	"\x11GetWebhookRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"/\n" +
	"\x13ListWebhooksRequest\x12\x18\n" +
	"\acreator\x18\x02 \x01(\tR\acreator\"I\n" +
	"\x14ListWebhooksResponse\x121\n" +
	"\bwebhooks\x18\x01 \x03(\v2\x15.memos.api.v1.WebhookR\bwebhooks\"\x84\x01\n" +
	"\x14UpdateWebhookRequest\x12/\n" +
	"\awebhook\x18\x01 \x01(\v2\x15.memos.api.v1.WebhookR\awebhook\x12;\n" +
	"\vupdate_mask\x18\x02 \x01(\v2\x1a.google.protobuf.FieldMaskR\n" +
	"updateMask\"&\n" +
	"\x14DeleteWebhookRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\xcd\x01\n" +
	"\x15WebhookRequestPayload\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12#\n" +
	"\ractivity_type\x18\x02 \x01(\tR\factivityType\x12\x18\n" +
	"\acreator\x18\x03 \x01(\tR\acreator\x12;\n" +
	"\vcreate_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12&\n" +
	"\x04memo\x18\x05 \x01(\v2\x12.memos.api.v1.MemoR\x04memo2\xd8\x04\n" +
	"\x0eWebhookService\x12g\n" +
	"\rCreateWebhook\x12\".memos.api.v1.CreateWebhookRequest\x1a\x15.memos.api.v1.Webhook\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/api/v1/webhooks\x12h\n" +
	"\n" +
	"GetWebhook\x12\x1f.memos.api.v1.GetWebhookRequest\x1a\x15.memos.api.v1.Webhook\"\"\xdaA\x02id\x82\xd3\xe4\x93\x02\x17\x12\x15/api/v1/webhooks/{id}\x12o\n" +
	"\fListWebhooks\x12!.memos.api.v1.ListWebhooksRequest\x1a\".memos.api.v1.ListWebhooksResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/v1/webhooks\x12\x90\x01\n" +
	"\rUpdateWebhook\x12\".memos.api.v1.UpdateWebhookRequest\x1a\x15.memos.api.v1.Webhook\"D\xdaA\x13webhook,update_mask\x82\xd3\xe4\x93\x02(:\awebhook2\x1d/api/v1/webhooks/{webhook.id}\x12o\n" +
	"\rDeleteWebhook\x12\".memos.api.v1.DeleteWebhookRequest\x1a\x16.google.protobuf.Empty\"\"\xdaA\x02id\x82\xd3\xe4\x93\x02\x17*\x15/api/v1/webhooks/{id}B\xab\x01\n" +
	"\x10com.memos.api.v1B\x13WebhookServiceProtoP\x01Z0github.com/usememos/memos/proto/gen/api/v1;apiv1\xa2\x02\x03MAX\xaa\x02\fMemos.Api.V1\xca\x02\fMemos\\Api\\V1\xe2\x02\x18Memos\\Api\\V1\\GPBMetadata\xea\x02\x0eMemos::Api::V1b\x06proto3"

var (
	file_api_v1_webhook_service_proto_rawDescOnce sync.Once
	file_api_v1_webhook_service_proto_rawDescData []byte
)

func file_api_v1_webhook_service_proto_rawDescGZIP() []byte {
	file_api_v1_webhook_service_proto_rawDescOnce.Do(func() {
		file_api_v1_webhook_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_v1_webhook_service_proto_rawDesc), len(file_api_v1_webhook_service_proto_rawDesc)))
	})
	return file_api_v1_webhook_service_proto_rawDescData
}

var file_api_v1_webhook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_v1_webhook_service_proto_goTypes = []any{
	(*Webhook)(nil),               // 0: memos.api.v1.Webhook
	(*CreateWebhookRequest)(nil),  // 1: memos.api.v1.CreateWebhookRequest
	(*GetWebhookRequest)(nil),     // 2: memos.api.v1.GetWebhookRequest
	(*ListWebhooksRequest)(nil),   // 3: memos.api.v1.ListWebhooksRequest
	(*ListWebhooksResponse)(nil),  // 4: memos.api.v1.ListWebhooksResponse
	(*UpdateWebhookRequest)(nil),  // 5: memos.api.v1.UpdateWebhookRequest
	(*DeleteWebhookRequest)(nil),  // 6: memos.api.v1.DeleteWebhookRequest
	(*WebhookRequestPayload)(nil), // 7: memos.api.v1.WebhookRequestPayload
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil), // 9: google.protobuf.FieldMask
	(*Memo)(nil),                  // 10: memos.api.v1.Memo
	(*emptypb.Empty)(nil),         // 11: google.protobuf.Empty
}
var file_api_v1_webhook_service_proto_depIdxs = []int32{
	8,  // 0: memos.api.v1.Webhook.create_time:type_name -> google.protobuf.Timestamp
	8,  // 1: memos.api.v1.Webhook.update_time:type_name -> google.protobuf.Timestamp
	0,  // 2: memos.api.v1.ListWebhooksResponse.webhooks:type_name -> memos.api.v1.Webhook
	0,  // 3: memos.api.v1.UpdateWebhookRequest.webhook:type_name -> memos.api.v1.Webhook
	9,  // 4: memos.api.v1.UpdateWebhookRequest.update_mask:type_name -> google.protobuf.FieldMask
	8,  // 5: memos.api.v1.WebhookRequestPayload.create_time:type_name -> google.protobuf.Timestamp
	10, // 6: memos.api.v1.WebhookRequestPayload.memo:type_name -> memos.api.v1.Memo
	1,  // 7: memos.api.v1.WebhookService.CreateWebhook:input_type -> memos.api.v1.CreateWebhookRequest
	2,  // 8: memos.api.v1.WebhookService.GetWebhook:input_type -> memos.api.v1.GetWebhookRequest
	3,  // 9: memos.api.v1.WebhookService.ListWebhooks:input_type -> memos.api.v1.ListWebhooksRequest
	5,  // 10: memos.api.v1.WebhookService.UpdateWebhook:input_type -> memos.api.v1.UpdateWebhookRequest
	6,  // 11: memos.api.v1.WebhookService.DeleteWebhook:input_type -> memos.api.v1.DeleteWebhookRequest
	0,  // 12: memos.api.v1.WebhookService.CreateWebhook:output_type -> memos.api.v1.Webhook
	0,  // 13: memos.api.v1.WebhookService.GetWebhook:output_type -> memos.api.v1.Webhook
	4,  // 14: memos.api.v1.WebhookService.ListWebhooks:output_type -> memos.api.v1.ListWebhooksResponse
	0,  // 15: memos.api.v1.WebhookService.UpdateWebhook:output_type -> memos.api.v1.Webhook
	11, // 16: memos.api.v1.WebhookService.DeleteWebhook:output_type -> google.protobuf.Empty
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_v1_webhook_service_proto_init() }
func file_api_v1_webhook_service_proto_init() {
	if File_api_v1_webhook_service_proto != nil {
		return
	}
	file_api_v1_memo_service_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_v1_webhook_service_proto_rawDesc), len(file_api_v1_webhook_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1_webhook_service_proto_goTypes,
		DependencyIndexes: file_api_v1_webhook_service_proto_depIdxs,
		MessageInfos:      file_api_v1_webhook_service_proto_msgTypes,
	}.Build()
	File_api_v1_webhook_service_proto = out.File
	file_api_v1_webhook_service_proto_goTypes = nil
	file_api_v1_webhook_service_proto_depIdxs = nil
}
