// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/v1/workspace_setting_service.proto

package apiv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WorkspaceSettingService_GetWorkspaceSetting_FullMethodName = "/memos.api.v1.WorkspaceSettingService/GetWorkspaceSetting"
	WorkspaceSettingService_SetWorkspaceSetting_FullMethodName = "/memos.api.v1.WorkspaceSettingService/SetWorkspaceSetting"
)

// WorkspaceSettingServiceClient is the client API for WorkspaceSettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkspaceSettingServiceClient interface {
	// GetWorkspaceSetting returns the setting by name.
	GetWorkspaceSetting(ctx context.Context, in *GetWorkspaceSettingRequest, opts ...grpc.CallOption) (*WorkspaceSetting, error)
	// SetWorkspaceSetting updates the setting.
	SetWorkspaceSetting(ctx context.Context, in *SetWorkspaceSettingRequest, opts ...grpc.CallOption) (*WorkspaceSetting, error)
}

type workspaceSettingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkspaceSettingServiceClient(cc grpc.ClientConnInterface) WorkspaceSettingServiceClient {
	return &workspaceSettingServiceClient{cc}
}

func (c *workspaceSettingServiceClient) GetWorkspaceSetting(ctx context.Context, in *GetWorkspaceSettingRequest, opts ...grpc.CallOption) (*WorkspaceSetting, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceSetting)
	err := c.cc.Invoke(ctx, WorkspaceSettingService_GetWorkspaceSetting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workspaceSettingServiceClient) SetWorkspaceSetting(ctx context.Context, in *SetWorkspaceSettingRequest, opts ...grpc.CallOption) (*WorkspaceSetting, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkspaceSetting)
	err := c.cc.Invoke(ctx, WorkspaceSettingService_SetWorkspaceSetting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkspaceSettingServiceServer is the server API for WorkspaceSettingService service.
// All implementations must embed UnimplementedWorkspaceSettingServiceServer
// for forward compatibility.
type WorkspaceSettingServiceServer interface {
	// GetWorkspaceSetting returns the setting by name.
	GetWorkspaceSetting(context.Context, *GetWorkspaceSettingRequest) (*WorkspaceSetting, error)
	// SetWorkspaceSetting updates the setting.
	SetWorkspaceSetting(context.Context, *SetWorkspaceSettingRequest) (*WorkspaceSetting, error)
	mustEmbedUnimplementedWorkspaceSettingServiceServer()
}

// UnimplementedWorkspaceSettingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWorkspaceSettingServiceServer struct{}

func (UnimplementedWorkspaceSettingServiceServer) GetWorkspaceSetting(context.Context, *GetWorkspaceSettingRequest) (*WorkspaceSetting, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceSetting not implemented")
}
func (UnimplementedWorkspaceSettingServiceServer) SetWorkspaceSetting(context.Context, *SetWorkspaceSettingRequest) (*WorkspaceSetting, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetWorkspaceSetting not implemented")
}
func (UnimplementedWorkspaceSettingServiceServer) mustEmbedUnimplementedWorkspaceSettingServiceServer() {
}
func (UnimplementedWorkspaceSettingServiceServer) testEmbeddedByValue() {}

// UnsafeWorkspaceSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkspaceSettingServiceServer will
// result in compilation errors.
type UnsafeWorkspaceSettingServiceServer interface {
	mustEmbedUnimplementedWorkspaceSettingServiceServer()
}

func RegisterWorkspaceSettingServiceServer(s grpc.ServiceRegistrar, srv WorkspaceSettingServiceServer) {
	// If the following call pancis, it indicates UnimplementedWorkspaceSettingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WorkspaceSettingService_ServiceDesc, srv)
}

func _WorkspaceSettingService_GetWorkspaceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceSettingServiceServer).GetWorkspaceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceSettingService_GetWorkspaceSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceSettingServiceServer).GetWorkspaceSetting(ctx, req.(*GetWorkspaceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkspaceSettingService_SetWorkspaceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWorkspaceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceSettingServiceServer).SetWorkspaceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkspaceSettingService_SetWorkspaceSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceSettingServiceServer).SetWorkspaceSetting(ctx, req.(*SetWorkspaceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkspaceSettingService_ServiceDesc is the grpc.ServiceDesc for WorkspaceSettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkspaceSettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "memos.api.v1.WorkspaceSettingService",
	HandlerType: (*WorkspaceSettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWorkspaceSetting",
			Handler:    _WorkspaceSettingService_GetWorkspaceSetting_Handler,
		},
		{
			MethodName: "SetWorkspaceSetting",
			Handler:    _WorkspaceSettingService_SetWorkspaceSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/v1/workspace_setting_service.proto",
}
