syntax = "proto3";

package memos.store;

option go_package = "gen/store";

enum UserSettingKey {
  USER_SETTING_KEY_UNSPECIFIED = 0;
  // Access tokens for the user.
  ACCESS_TOKENS = 1;
  // The locale of the user.
  LOCALE = 2;
  // The appearance of the user.
  APPEARANCE = 3;
  // The visibility of the memo.
  MEMO_VISIBILITY = 4;
  // The shortcuts of the user.
  SHORTCUTS = 5;
}

message UserSetting {
  int32 user_id = 1;
  UserSettingKey key = 2;
  oneof value {
    AccessTokensUserSetting access_tokens = 3;
    string locale = 4;
    string appearance = 5;
    string memo_visibility = 6;
    ShortcutsUserSetting shortcuts = 7;
  }
}

message AccessTokensUserSetting {
  message AccessToken {
    // The access token is a JWT token.
    // Including expiration time, issuer, etc.
    string access_token = 1;
    // A description for the access token.
    string description = 2;
  }
  repeated AccessToken access_tokens = 1;
}

message ShortcutsUserSetting {
  message Shortcut {
    string id = 1;
    string title = 2;
    string filter = 3;
  }
  repeated Shortcut shortcuts = 1;
}
