syntax = "proto3";

package memos.store;

option go_package = "gen/store";

message IdentityProvider {
  int32 id = 1;
  string name = 2;

  enum Type {
    TYPE_UNSPECIFIED = 0;
    OAUTH2 = 1;
  }
  Type type = 3;
  string identifier_filter = 4;
  IdentityProviderConfig config = 5;
}

message IdentityProviderConfig {
  oneof config {
    OAuth2Config oauth2_config = 1;
  }
}

message FieldMapping {
  string identifier = 1;
  string display_name = 2;
  string email = 3;
  string avatar_url = 4;
}

message OAuth2Config {
  string client_id = 1;
  string client_secret = 2;
  string auth_url = 3;
  string token_url = 4;
  string user_info_url = 5;
  repeated string scopes = 6;
  FieldMapping field_mapping = 7;
}
