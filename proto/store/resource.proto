syntax = "proto3";

package memos.store;

import "google/protobuf/timestamp.proto";
import "store/workspace_setting.proto";

option go_package = "gen/store";

enum ResourceStorageType {
  RESOURCE_STORAGE_TYPE_UNSPECIFIED = 0;
  // Resource is stored locally. AKA, local file system.
  LOCAL = 1;
  // Resource is stored in S3.
  S3 = 2;
  // Resource is stored in an external storage. The reference is a URL.
  EXTERNAL = 3;
}

message ResourcePayload {
  oneof payload {
    S3Object s3_object = 1;
  }

  message S3Object {
    StorageS3Config s3_config = 1;
    // key is the S3 object key.
    string key = 2;
    // last_presigned_time is the last time the object was presigned.
    // This is used to determine if the presigned URL is still valid.
    google.protobuf.Timestamp last_presigned_time = 3;
  }
}
