syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";

option go_package = "gen/api/v1";

service WorkspaceService {
  // GetWorkspaceProfile returns the workspace profile.
  rpc GetWorkspaceProfile(GetWorkspaceProfileRequest) returns (WorkspaceProfile) {
    option (google.api.http) = {get: "/api/v1/workspace/profile"};
  }
}

message WorkspaceProfile {
  // The name of instance owner.
  // Format: users/{user}
  string owner = 1;
  // version is the current version of instance
  string version = 2;
  // mode is the instance mode (e.g. "prod", "dev" or "demo").
  string mode = 3;
  // instance_url is the URL of the instance.
  string instance_url = 6;
}

message GetWorkspaceProfileRequest {}
