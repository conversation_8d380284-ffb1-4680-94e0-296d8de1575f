syntax = "proto3";

package memos.api.v1;

import "api/v1/user_service.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "gen/api/v1";

service AuthService {
  // GetAuthStatus returns the current auth status of the user.
  rpc GetAuthStatus(GetAuthStatusRequest) returns (User) {
    option (google.api.http) = {post: "/api/v1/auth/status"};
  }
  // SignIn signs in the user.
  rpc SignIn(SignInRequest) returns (User) {
    option (google.api.http) = {post: "/api/v1/auth/signin"};
  }
  // SignUp signs up the user with the given username and password.
  rpc SignUp(SignUpRequest) returns (User) {
    option (google.api.http) = {post: "/api/v1/auth/signup"};
  }
  // SignOut signs out the user.
  rpc SignOut(SignOutRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {post: "/api/v1/auth/signout"};
  }
}

message GetAuthStatusRequest {}

message GetAuthStatusResponse {
  User user = 1;
}

message SignInRequest {
  // Provide one authentication method (username/password or SSO).
  oneof method {
    // Username and password authentication method.
    PasswordCredentials password_credentials = 1;

    // SSO provider authentication method.
    SSOCredentials sso_credentials = 2;
  }
  // Whether the session should never expire.
  bool never_expire = 3;
}

message PasswordCredentials {
  // The username to sign in with.
  string username = 1;
  // The password to sign in with.
  string password = 2;
}

message SSOCredentials {
  // The ID of the SSO provider.
  int32 idp_id = 1;
  // The code to sign in with.
  string code = 2;
  // The redirect URI.
  string redirect_uri = 3;
}

message SignUpRequest {
  // The username to sign up with.
  string username = 1;
  // The password to sign up with.
  string password = 2;
}

message SignOutRequest {}
