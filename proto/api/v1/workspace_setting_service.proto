syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option go_package = "gen/api/v1";

service WorkspaceSettingService {
  // GetWorkspaceSetting returns the setting by name.
  rpc GetWorkspaceSetting(GetWorkspaceSettingRequest) returns (WorkspaceSetting) {
    option (google.api.http) = {get: "/api/v1/workspace/{name=settings/*}"};
    option (google.api.method_signature) = "name";
  }
  // SetWorkspaceSetting updates the setting.
  rpc SetWorkspaceSetting(SetWorkspaceSettingRequest) returns (WorkspaceSetting) {
    option (google.api.http) = {
      patch: "/api/v1/workspace/{setting.name=settings/*}"
      body: "setting"
    };
    option (google.api.method_signature) = "setting";
  }
}

message WorkspaceSetting {
  // name is the name of the setting.
  // Format: settings/{setting}
  string name = 1;
  oneof value {
    WorkspaceGeneralSetting general_setting = 2;
    WorkspaceStorageSetting storage_setting = 3;
    WorkspaceMemoRelatedSetting memo_related_setting = 4;
  }
}

message WorkspaceGeneralSetting {
  // disallow_user_registration disallows user registration.
  bool disallow_user_registration = 1;
  // disallow_password_auth disallows password authentication.
  bool disallow_password_auth = 2;
  // additional_script is the additional script.
  string additional_script = 3;
  // additional_style is the additional style.
  string additional_style = 4;
  // custom_profile is the custom profile.
  WorkspaceCustomProfile custom_profile = 5;
  // week_start_day_offset is the week start day offset from Sunday.
  // 0: Sunday, 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday
  // Default is Sunday.
  int32 week_start_day_offset = 6;

  // disallow_change_username disallows changing username.
  bool disallow_change_username = 7;
  // disallow_change_nickname disallows changing nickname.
  bool disallow_change_nickname = 8;
}

message WorkspaceCustomProfile {
  string title = 1;
  string description = 2;
  string logo_url = 3;
  string locale = 4;
  string appearance = 5;
}

message WorkspaceStorageSetting {
  enum StorageType {
    STORAGE_TYPE_UNSPECIFIED = 0;
    // DATABASE is the database storage type.
    DATABASE = 1;
    // LOCAL is the local storage type.
    LOCAL = 2;
    // S3 is the S3 storage type.
    S3 = 3;
  }
  // storage_type is the storage type.
  StorageType storage_type = 1;
  // The template of file path.
  // e.g. assets/{timestamp}_{filename}
  string filepath_template = 2;
  // The max upload size in megabytes.
  int64 upload_size_limit_mb = 3;
  // Reference: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-go/
  message S3Config {
    string access_key_id = 1;
    string access_key_secret = 2;
    string endpoint = 3;
    string region = 4;
    string bucket = 5;
    bool use_path_style = 6;
  }
  // The S3 config.
  S3Config s3_config = 4;
}

message WorkspaceMemoRelatedSetting {
  reserved 4, 8;

  // disallow_public_visibility disallows set memo as public visibility.
  bool disallow_public_visibility = 1;
  // display_with_update_time orders and displays memo with update time.
  bool display_with_update_time = 2;
  // content_length_limit is the limit of content length. Unit is byte.
  int32 content_length_limit = 3;
  // enable_double_click_edit enables editing on double click.
  bool enable_double_click_edit = 5;
  // enable_link_preview enables links preview.
  bool enable_link_preview = 6;
  // enable_comment enables comment.
  bool enable_comment = 7;
  // reactions is the list of reactions.
  repeated string reactions = 10;
  // disable_markdown_shortcuts disallow the registration of markdown shortcuts.
  bool disable_markdown_shortcuts = 11;
  // enable_blur_nsfw_content enables blurring of content marked as not safe for work (NSFW).
  bool enable_blur_nsfw_content = 12;
  // nsfw_tags is the list of tags that mark content as NSFW for blurring.
  repeated string nsfw_tags = 13;
}

message GetWorkspaceSettingRequest {
  // The resource name of the workspace setting.
  // Format: settings/{setting}
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

message SetWorkspaceSettingRequest {
  // setting is the setting to update.
  WorkspaceSetting setting = 1;
}
