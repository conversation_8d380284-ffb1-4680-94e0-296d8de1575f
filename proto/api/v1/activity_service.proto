syntax = "proto3";

package memos.api.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gen/api/v1";

service ActivityService {
  // GetActivity returns the activity with the given id.
  rpc GetActivity(GetActivityRequest) returns (Activity) {
    option (google.api.http) = {get: "/api/v1/{name=activities/*}"};
    option (google.api.method_signature) = "name";
  }
}

message Activity {
  // The name of the activity.
  // Format: activities/{id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];
  // The name of the creator.
  // Format: users/{user}
  string creator = 2;
  // The type of the activity.
  string type = 3;
  // The level of the activity.
  string level = 4;
  // The create time of the activity.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The payload of the activity.
  ActivityPayload payload = 6;
}

message ActivityPayload {
  ActivityMemoCommentPayload memo_comment = 1;
}

// ActivityMemoCommentPayload represents the payload of a memo comment activity.
message ActivityMemoCommentPayload {
  // The memo name of comment.
  // Refer to `Memo.name`.
  string memo = 1;
  // The name of related memo.
  string related_memo = 2;
}

message GetActivityRequest {
  // The name of the activity.
  // Format: activities/{id}, id is the system generated auto-incremented id.
  string name = 1;
}
