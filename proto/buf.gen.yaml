version: v2
managed:
  enabled: true
  disable:
    - file_option: go_package
      module: buf.build/googleapis/googleapis
  override:
    - file_option: go_package_prefix
      value: github.com/usememos/memos/proto/gen
plugins:
  - remote: buf.build/protocolbuffers/go
    out: gen
    opt: paths=source_relative
  - remote: buf.build/grpc/go
    out: gen
    opt: paths=source_relative
  - remote: buf.build/grpc-ecosystem/gateway
    out: gen
    opt: paths=source_relative
  - remote: buf.build/grpc-ecosystem/openapiv2
    out: gen
    opt: output_format=yaml,allow_merge=true
  - remote: buf.build/community/stephenh-ts-proto
    out: ../web/src/types/proto
    opt:
      - env=browser
      - useOptionals=messages
      - outputServices=generic-definitions
      - outputJsonMethods=false
      - useExactTypes=false
      - esModuleInterop=true
      - stringEnums=true
