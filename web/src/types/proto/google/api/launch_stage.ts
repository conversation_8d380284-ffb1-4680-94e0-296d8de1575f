// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: google/api/launch_stage.proto

/* eslint-disable */

export const protobufPackage = "google.api";

/**
 * The launch stage as defined by [Google Cloud Platform
 * Launch Stages](https://cloud.google.com/terms/launch-stages).
 */
export enum LaunchStage {
  /** LAUNCH_STAGE_UNSPECIFIED - Do not use this default value. */
  LAUNCH_STAGE_UNSPECIFIED = "LAUNCH_STAGE_UNSPECIFIED",
  /** UNIMPLEMENTED - The feature is not yet implemented. Users can not use it. */
  UNIMPLEMENTED = "UNIMPLEMENTED",
  /** PRELAUNCH - Prelaunch features are hidden from users and are only visible internally. */
  PRELAUNCH = "PRELAUNCH",
  /**
   * EARLY_ACCESS - Early Access features are limited to a closed group of testers. To use
   * these features, you must sign up in advance and sign a Trusted Tester
   * agreement (which includes confidentiality provisions). These features may
   * be unstable, changed in backward-incompatible ways, and are not
   * guaranteed to be released.
   */
  EARLY_ACCESS = "EARLY_ACCESS",
  /**
   * ALPHA - Alpha is a limited availability test for releases before they are cleared
   * for widespread use. By Alpha, all significant design issues are resolved
   * and we are in the process of verifying functionality. Alpha customers
   * need to apply for access, agree to applicable terms, and have their
   * projects allowlisted. Alpha releases don't have to be feature complete,
   * no SLAs are provided, and there are no technical support obligations, but
   * they will be far enough along that customers can actually use them in
   * test environments or for limited-use tests -- just like they would in
   * normal production cases.
   */
  ALPHA = "ALPHA",
  /**
   * BETA - Beta is the point at which we are ready to open a release for any
   * customer to use. There are no SLA or technical support obligations in a
   * Beta release. Products will be complete from a feature perspective, but
   * may have some open outstanding issues. Beta releases are suitable for
   * limited production use cases.
   */
  BETA = "BETA",
  /**
   * GA - GA features are open to all developers and are considered stable and
   * fully qualified for production use.
   */
  GA = "GA",
  /**
   * DEPRECATED - Deprecated features are scheduled to be shut down and removed. For more
   * information, see the "Deprecation Policy" section of our [Terms of
   * Service](https://cloud.google.com/terms/)
   * and the [Google Cloud Platform Subject to the Deprecation
   * Policy](https://cloud.google.com/terms/deprecation) documentation.
   */
  DEPRECATED = "DEPRECATED",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function launchStageFromJSON(object: any): LaunchStage {
  switch (object) {
    case 0:
    case "LAUNCH_STAGE_UNSPECIFIED":
      return LaunchStage.LAUNCH_STAGE_UNSPECIFIED;
    case 6:
    case "UNIMPLEMENTED":
      return LaunchStage.UNIMPLEMENTED;
    case 7:
    case "PRELAUNCH":
      return LaunchStage.PRELAUNCH;
    case 1:
    case "EARLY_ACCESS":
      return LaunchStage.EARLY_ACCESS;
    case 2:
    case "ALPHA":
      return LaunchStage.ALPHA;
    case 3:
    case "BETA":
      return LaunchStage.BETA;
    case 4:
    case "GA":
      return LaunchStage.GA;
    case 5:
    case "DEPRECATED":
      return LaunchStage.DEPRECATED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return LaunchStage.UNRECOGNIZED;
  }
}

export function launchStageToNumber(object: LaunchStage): number {
  switch (object) {
    case LaunchStage.LAUNCH_STAGE_UNSPECIFIED:
      return 0;
    case LaunchStage.UNIMPLEMENTED:
      return 6;
    case LaunchStage.PRELAUNCH:
      return 7;
    case LaunchStage.EARLY_ACCESS:
      return 1;
    case LaunchStage.ALPHA:
      return 2;
    case LaunchStage.BETA:
      return 3;
    case LaunchStage.GA:
      return 4;
    case LaunchStage.DEPRECATED:
      return 5;
    case LaunchStage.UNRECOGNIZED:
    default:
      return -1;
  }
}
