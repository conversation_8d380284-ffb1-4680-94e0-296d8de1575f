// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: google/api/client.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Duration } from "../protobuf/duration";
import { LaunchStage, launchStageFromJSON, launchStageToNumber } from "./launch_stage";

export const protobufPackage = "google.api";

/**
 * The organization for which the client libraries are being published.
 * Affects the url where generated docs are published, etc.
 */
export enum ClientLibraryOrganization {
  /** CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED - Not useful. */
  CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED = "CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED",
  /** CLOUD - Google Cloud Platform Org. */
  CLOUD = "CLOUD",
  /** ADS - Ads (Advertising) Org. */
  ADS = "ADS",
  /** PHOTOS - Photos Org. */
  PHOTOS = "PHOTOS",
  /** STREET_VIEW - Street View Org. */
  STREET_VIEW = "STREET_VIEW",
  /** SHOPPING - Shopping Org. */
  SHOPPING = "SHOPPING",
  /** GEO - Geo Org. */
  GEO = "GEO",
  /** GENERATIVE_AI - Generative AI - https://developers.generativeai.google */
  GENERATIVE_AI = "GENERATIVE_AI",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function clientLibraryOrganizationFromJSON(object: any): ClientLibraryOrganization {
  switch (object) {
    case 0:
    case "CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":
      return ClientLibraryOrganization.CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED;
    case 1:
    case "CLOUD":
      return ClientLibraryOrganization.CLOUD;
    case 2:
    case "ADS":
      return ClientLibraryOrganization.ADS;
    case 3:
    case "PHOTOS":
      return ClientLibraryOrganization.PHOTOS;
    case 4:
    case "STREET_VIEW":
      return ClientLibraryOrganization.STREET_VIEW;
    case 5:
    case "SHOPPING":
      return ClientLibraryOrganization.SHOPPING;
    case 6:
    case "GEO":
      return ClientLibraryOrganization.GEO;
    case 7:
    case "GENERATIVE_AI":
      return ClientLibraryOrganization.GENERATIVE_AI;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClientLibraryOrganization.UNRECOGNIZED;
  }
}

export function clientLibraryOrganizationToNumber(object: ClientLibraryOrganization): number {
  switch (object) {
    case ClientLibraryOrganization.CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED:
      return 0;
    case ClientLibraryOrganization.CLOUD:
      return 1;
    case ClientLibraryOrganization.ADS:
      return 2;
    case ClientLibraryOrganization.PHOTOS:
      return 3;
    case ClientLibraryOrganization.STREET_VIEW:
      return 4;
    case ClientLibraryOrganization.SHOPPING:
      return 5;
    case ClientLibraryOrganization.GEO:
      return 6;
    case ClientLibraryOrganization.GENERATIVE_AI:
      return 7;
    case ClientLibraryOrganization.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** To where should client libraries be published? */
export enum ClientLibraryDestination {
  /**
   * CLIENT_LIBRARY_DESTINATION_UNSPECIFIED - Client libraries will neither be generated nor published to package
   * managers.
   */
  CLIENT_LIBRARY_DESTINATION_UNSPECIFIED = "CLIENT_LIBRARY_DESTINATION_UNSPECIFIED",
  /**
   * GITHUB - Generate the client library in a repo under github.com/googleapis,
   * but don't publish it to package managers.
   */
  GITHUB = "GITHUB",
  /** PACKAGE_MANAGER - Publish the library to package managers like nuget.org and npmjs.com. */
  PACKAGE_MANAGER = "PACKAGE_MANAGER",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function clientLibraryDestinationFromJSON(object: any): ClientLibraryDestination {
  switch (object) {
    case 0:
    case "CLIENT_LIBRARY_DESTINATION_UNSPECIFIED":
      return ClientLibraryDestination.CLIENT_LIBRARY_DESTINATION_UNSPECIFIED;
    case 10:
    case "GITHUB":
      return ClientLibraryDestination.GITHUB;
    case 20:
    case "PACKAGE_MANAGER":
      return ClientLibraryDestination.PACKAGE_MANAGER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClientLibraryDestination.UNRECOGNIZED;
  }
}

export function clientLibraryDestinationToNumber(object: ClientLibraryDestination): number {
  switch (object) {
    case ClientLibraryDestination.CLIENT_LIBRARY_DESTINATION_UNSPECIFIED:
      return 0;
    case ClientLibraryDestination.GITHUB:
      return 10;
    case ClientLibraryDestination.PACKAGE_MANAGER:
      return 20;
    case ClientLibraryDestination.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Required information for every language. */
export interface CommonLanguageSettings {
  /**
   * Link to automatically generated reference documentation.  Example:
   * https://cloud.google.com/nodejs/docs/reference/asset/latest
   *
   * @deprecated
   */
  referenceDocsUri: string;
  /** The destination where API teams want this client library to be published. */
  destinations: ClientLibraryDestination[];
  /** Configuration for which RPCs should be generated in the GAPIC client. */
  selectiveGapicGeneration?: SelectiveGapicGeneration | undefined;
}

/** Details about how and where to publish client libraries. */
export interface ClientLibrarySettings {
  /**
   * Version of the API to apply these settings to. This is the full protobuf
   * package for the API, ending in the version element.
   * Examples: "google.cloud.speech.v1" and "google.spanner.admin.database.v1".
   */
  version: string;
  /** Launch stage of this version of the API. */
  launchStage: LaunchStage;
  /**
   * When using transport=rest, the client request will encode enums as
   * numbers rather than strings.
   */
  restNumericEnums: boolean;
  /** Settings for legacy Java features, supported in the Service YAML. */
  javaSettings?:
    | JavaSettings
    | undefined;
  /** Settings for C++ client libraries. */
  cppSettings?:
    | CppSettings
    | undefined;
  /** Settings for PHP client libraries. */
  phpSettings?:
    | PhpSettings
    | undefined;
  /** Settings for Python client libraries. */
  pythonSettings?:
    | PythonSettings
    | undefined;
  /** Settings for Node client libraries. */
  nodeSettings?:
    | NodeSettings
    | undefined;
  /** Settings for .NET client libraries. */
  dotnetSettings?:
    | DotnetSettings
    | undefined;
  /** Settings for Ruby client libraries. */
  rubySettings?:
    | RubySettings
    | undefined;
  /** Settings for Go client libraries. */
  goSettings?: GoSettings | undefined;
}

/**
 * This message configures the settings for publishing [Google Cloud Client
 * libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
 * generated from the service config.
 */
export interface Publishing {
  /**
   * A list of API method settings, e.g. the behavior for methods that use the
   * long-running operation pattern.
   */
  methodSettings: MethodSettings[];
  /**
   * Link to a *public* URI where users can report issues.  Example:
   * https://issuetracker.google.com/issues/new?component=190865&template=1161103
   */
  newIssueUri: string;
  /**
   * Link to product home page.  Example:
   * https://cloud.google.com/asset-inventory/docs/overview
   */
  documentationUri: string;
  /**
   * Used as a tracking tag when collecting data about the APIs developer
   * relations artifacts like docs, packages delivered to package managers,
   * etc.  Example: "speech".
   */
  apiShortName: string;
  /** GitHub label to apply to issues and pull requests opened for this API. */
  githubLabel: string;
  /**
   * GitHub teams to be added to CODEOWNERS in the directory in GitHub
   * containing source code for the client libraries for this API.
   */
  codeownerGithubTeams: string[];
  /**
   * A prefix used in sample code when demarking regions to be included in
   * documentation.
   */
  docTagPrefix: string;
  /** For whom the client library is being published. */
  organization: ClientLibraryOrganization;
  /**
   * Client library settings.  If the same version string appears multiple
   * times in this list, then the last one wins.  Settings from earlier
   * settings with the same version string are discarded.
   */
  librarySettings: ClientLibrarySettings[];
  /**
   * Optional link to proto reference documentation.  Example:
   * https://cloud.google.com/pubsub/lite/docs/reference/rpc
   */
  protoReferenceDocumentationUri: string;
  /**
   * Optional link to REST reference documentation.  Example:
   * https://cloud.google.com/pubsub/lite/docs/reference/rest
   */
  restReferenceDocumentationUri: string;
}

/** Settings for Java client libraries. */
export interface JavaSettings {
  /**
   * The package name to use in Java. Clobbers the java_package option
   * set in the protobuf. This should be used **only** by APIs
   * who have already set the language_settings.java.package_name" field
   * in gapic.yaml. API teams should use the protobuf java_package option
   * where possible.
   *
   * Example of a YAML configuration::
   *
   *  publishing:
   *    java_settings:
   *      library_package: com.google.cloud.pubsub.v1
   */
  libraryPackage: string;
  /**
   * Configure the Java class name to use instead of the service's for its
   * corresponding generated GAPIC client. Keys are fully-qualified
   * service names as they appear in the protobuf (including the full
   * the language_settings.java.interface_names" field in gapic.yaml. API
   * teams should otherwise use the service name as it appears in the
   * protobuf.
   *
   * Example of a YAML configuration::
   *
   *  publishing:
   *    java_settings:
   *      service_class_names:
   *        - google.pubsub.v1.Publisher: TopicAdmin
   *        - google.pubsub.v1.Subscriber: SubscriptionAdmin
   */
  serviceClassNames: { [key: string]: string };
  /** Some settings. */
  common?: CommonLanguageSettings | undefined;
}

export interface JavaSettings_ServiceClassNamesEntry {
  key: string;
  value: string;
}

/** Settings for C++ client libraries. */
export interface CppSettings {
  /** Some settings. */
  common?: CommonLanguageSettings | undefined;
}

/** Settings for Php client libraries. */
export interface PhpSettings {
  /** Some settings. */
  common?: CommonLanguageSettings | undefined;
}

/** Settings for Python client libraries. */
export interface PythonSettings {
  /** Some settings. */
  common?:
    | CommonLanguageSettings
    | undefined;
  /** Experimental features to be included during client library generation. */
  experimentalFeatures?: PythonSettings_ExperimentalFeatures | undefined;
}

/**
 * Experimental features to be included during client library generation.
 * These fields will be deprecated once the feature graduates and is enabled
 * by default.
 */
export interface PythonSettings_ExperimentalFeatures {
  /**
   * Enables generation of asynchronous REST clients if `rest` transport is
   * enabled. By default, asynchronous REST clients will not be generated.
   * This feature will be enabled by default 1 month after launching the
   * feature in preview packages.
   */
  restAsyncIoEnabled: boolean;
  /**
   * Enables generation of protobuf code using new types that are more
   * Pythonic which are included in `protobuf>=5.29.x`. This feature will be
   * enabled by default 1 month after launching the feature in preview
   * packages.
   */
  protobufPythonicTypesEnabled: boolean;
  /**
   * Disables generation of an unversioned Python package for this client
   * library. This means that the module names will need to be versioned in
   * import statements. For example `import google.cloud.library_v2` instead
   * of `import google.cloud.library`.
   */
  unversionedPackageDisabled: boolean;
}

/** Settings for Node client libraries. */
export interface NodeSettings {
  /** Some settings. */
  common?: CommonLanguageSettings | undefined;
}

/** Settings for Dotnet client libraries. */
export interface DotnetSettings {
  /** Some settings. */
  common?:
    | CommonLanguageSettings
    | undefined;
  /**
   * Map from original service names to renamed versions.
   * This is used when the default generated types
   * would cause a naming conflict. (Neither name is
   * fully-qualified.)
   * Example: Subscriber to SubscriberServiceApi.
   */
  renamedServices: { [key: string]: string };
  /**
   * Map from full resource types to the effective short name
   * for the resource. This is used when otherwise resource
   * named from different services would cause naming collisions.
   * Example entry:
   * "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
   */
  renamedResources: { [key: string]: string };
  /**
   * List of full resource types to ignore during generation.
   * This is typically used for API-specific Location resources,
   * which should be handled by the generator as if they were actually
   * the common Location resources.
   * Example entry: "documentai.googleapis.com/Location"
   */
  ignoredResources: string[];
  /**
   * Namespaces which must be aliased in snippets due to
   * a known (but non-generator-predictable) naming collision
   */
  forcedNamespaceAliases: string[];
  /**
   * Method signatures (in the form "service.method(signature)")
   * which are provided separately, so shouldn't be generated.
   * Snippets *calling* these methods are still generated, however.
   */
  handwrittenSignatures: string[];
}

export interface DotnetSettings_RenamedServicesEntry {
  key: string;
  value: string;
}

export interface DotnetSettings_RenamedResourcesEntry {
  key: string;
  value: string;
}

/** Settings for Ruby client libraries. */
export interface RubySettings {
  /** Some settings. */
  common?: CommonLanguageSettings | undefined;
}

/** Settings for Go client libraries. */
export interface GoSettings {
  /** Some settings. */
  common?:
    | CommonLanguageSettings
    | undefined;
  /**
   * Map of service names to renamed services. Keys are the package relative
   * service names and values are the name to be used for the service client
   * and call options.
   *
   * publishing:
   *   go_settings:
   *     renamed_services:
   *       Publisher: TopicAdmin
   */
  renamedServices: { [key: string]: string };
}

export interface GoSettings_RenamedServicesEntry {
  key: string;
  value: string;
}

/** Describes the generator configuration for a method. */
export interface MethodSettings {
  /**
   * The fully qualified name of the method, for which the options below apply.
   * This is used to find the method to apply the options.
   *
   * Example:
   *
   *    publishing:
   *      method_settings:
   *      - selector: google.storage.control.v2.StorageControl.CreateFolder
   *        # method settings for CreateFolder...
   */
  selector: string;
  /**
   * Describes settings to use for long-running operations when generating
   * API methods for RPCs. Complements RPCs that use the annotations in
   * google/longrunning/operations.proto.
   *
   * Example of a YAML configuration::
   *
   *    publishing:
   *      method_settings:
   *      - selector: google.cloud.speech.v2.Speech.BatchRecognize
   *        long_running:
   *          initial_poll_delay: 60s # 1 minute
   *          poll_delay_multiplier: 1.5
   *          max_poll_delay: 360s # 6 minutes
   *          total_poll_timeout: 54000s # 90 minutes
   */
  longRunning?:
    | MethodSettings_LongRunning
    | undefined;
  /**
   * List of top-level fields of the request message, that should be
   * automatically populated by the client libraries based on their
   * (google.api.field_info).format. Currently supported format: UUID4.
   *
   * Example of a YAML configuration:
   *
   *    publishing:
   *      method_settings:
   *      - selector: google.example.v1.ExampleService.CreateExample
   *        auto_populated_fields:
   *        - request_id
   */
  autoPopulatedFields: string[];
}

/**
 * Describes settings to use when generating API methods that use the
 * long-running operation pattern.
 * All default values below are from those used in the client library
 * generators (e.g.
 * [Java](https://github.com/googleapis/gapic-generator-java/blob/04c2faa191a9b5a10b92392fe8482279c4404803/src/main/java/com/google/api/generator/gapic/composer/common/RetrySettingsComposer.java)).
 */
export interface MethodSettings_LongRunning {
  /**
   * Initial delay after which the first poll request will be made.
   * Default value: 5 seconds.
   */
  initialPollDelay?:
    | Duration
    | undefined;
  /**
   * Multiplier to gradually increase delay between subsequent polls until it
   * reaches max_poll_delay.
   * Default value: 1.5.
   */
  pollDelayMultiplier: number;
  /**
   * Maximum time between two subsequent poll requests.
   * Default value: 45 seconds.
   */
  maxPollDelay?:
    | Duration
    | undefined;
  /**
   * Total polling timeout.
   * Default value: 5 minutes.
   */
  totalPollTimeout?: Duration | undefined;
}

/**
 * This message is used to configure the generation of a subset of the RPCs in
 * a service for client libraries.
 */
export interface SelectiveGapicGeneration {
  /**
   * An allowlist of the fully qualified names of RPCs that should be included
   * on public client surfaces.
   */
  methods: string[];
  /**
   * Setting this to true indicates to the client generators that methods
   * that would be excluded from the generation should instead be generated
   * in a way that indicates these methods should not be consumed by
   * end users. How this is expressed is up to individual language
   * implementations to decide. Some examples may be: added annotations,
   * obfuscated identifiers, or other language idiomatic patterns.
   */
  generateOmittedAsInternal: boolean;
}

function createBaseCommonLanguageSettings(): CommonLanguageSettings {
  return { referenceDocsUri: "", destinations: [], selectiveGapicGeneration: undefined };
}

export const CommonLanguageSettings: MessageFns<CommonLanguageSettings> = {
  encode(message: CommonLanguageSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referenceDocsUri !== "") {
      writer.uint32(10).string(message.referenceDocsUri);
    }
    writer.uint32(18).fork();
    for (const v of message.destinations) {
      writer.int32(clientLibraryDestinationToNumber(v));
    }
    writer.join();
    if (message.selectiveGapicGeneration !== undefined) {
      SelectiveGapicGeneration.encode(message.selectiveGapicGeneration, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommonLanguageSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommonLanguageSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referenceDocsUri = reader.string();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.destinations.push(clientLibraryDestinationFromJSON(reader.int32()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.destinations.push(clientLibraryDestinationFromJSON(reader.int32()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.selectiveGapicGeneration = SelectiveGapicGeneration.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CommonLanguageSettings>): CommonLanguageSettings {
    return CommonLanguageSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CommonLanguageSettings>): CommonLanguageSettings {
    const message = createBaseCommonLanguageSettings();
    message.referenceDocsUri = object.referenceDocsUri ?? "";
    message.destinations = object.destinations?.map((e) => e) || [];
    message.selectiveGapicGeneration =
      (object.selectiveGapicGeneration !== undefined && object.selectiveGapicGeneration !== null)
        ? SelectiveGapicGeneration.fromPartial(object.selectiveGapicGeneration)
        : undefined;
    return message;
  },
};

function createBaseClientLibrarySettings(): ClientLibrarySettings {
  return {
    version: "",
    launchStage: LaunchStage.LAUNCH_STAGE_UNSPECIFIED,
    restNumericEnums: false,
    javaSettings: undefined,
    cppSettings: undefined,
    phpSettings: undefined,
    pythonSettings: undefined,
    nodeSettings: undefined,
    dotnetSettings: undefined,
    rubySettings: undefined,
    goSettings: undefined,
  };
}

export const ClientLibrarySettings: MessageFns<ClientLibrarySettings> = {
  encode(message: ClientLibrarySettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.version !== "") {
      writer.uint32(10).string(message.version);
    }
    if (message.launchStage !== LaunchStage.LAUNCH_STAGE_UNSPECIFIED) {
      writer.uint32(16).int32(launchStageToNumber(message.launchStage));
    }
    if (message.restNumericEnums !== false) {
      writer.uint32(24).bool(message.restNumericEnums);
    }
    if (message.javaSettings !== undefined) {
      JavaSettings.encode(message.javaSettings, writer.uint32(170).fork()).join();
    }
    if (message.cppSettings !== undefined) {
      CppSettings.encode(message.cppSettings, writer.uint32(178).fork()).join();
    }
    if (message.phpSettings !== undefined) {
      PhpSettings.encode(message.phpSettings, writer.uint32(186).fork()).join();
    }
    if (message.pythonSettings !== undefined) {
      PythonSettings.encode(message.pythonSettings, writer.uint32(194).fork()).join();
    }
    if (message.nodeSettings !== undefined) {
      NodeSettings.encode(message.nodeSettings, writer.uint32(202).fork()).join();
    }
    if (message.dotnetSettings !== undefined) {
      DotnetSettings.encode(message.dotnetSettings, writer.uint32(210).fork()).join();
    }
    if (message.rubySettings !== undefined) {
      RubySettings.encode(message.rubySettings, writer.uint32(218).fork()).join();
    }
    if (message.goSettings !== undefined) {
      GoSettings.encode(message.goSettings, writer.uint32(226).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ClientLibrarySettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientLibrarySettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.version = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.launchStage = launchStageFromJSON(reader.int32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.restNumericEnums = reader.bool();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.javaSettings = JavaSettings.decode(reader, reader.uint32());
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.cppSettings = CppSettings.decode(reader, reader.uint32());
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.phpSettings = PhpSettings.decode(reader, reader.uint32());
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.pythonSettings = PythonSettings.decode(reader, reader.uint32());
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.nodeSettings = NodeSettings.decode(reader, reader.uint32());
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.dotnetSettings = DotnetSettings.decode(reader, reader.uint32());
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.rubySettings = RubySettings.decode(reader, reader.uint32());
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.goSettings = GoSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ClientLibrarySettings>): ClientLibrarySettings {
    return ClientLibrarySettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ClientLibrarySettings>): ClientLibrarySettings {
    const message = createBaseClientLibrarySettings();
    message.version = object.version ?? "";
    message.launchStage = object.launchStage ?? LaunchStage.LAUNCH_STAGE_UNSPECIFIED;
    message.restNumericEnums = object.restNumericEnums ?? false;
    message.javaSettings = (object.javaSettings !== undefined && object.javaSettings !== null)
      ? JavaSettings.fromPartial(object.javaSettings)
      : undefined;
    message.cppSettings = (object.cppSettings !== undefined && object.cppSettings !== null)
      ? CppSettings.fromPartial(object.cppSettings)
      : undefined;
    message.phpSettings = (object.phpSettings !== undefined && object.phpSettings !== null)
      ? PhpSettings.fromPartial(object.phpSettings)
      : undefined;
    message.pythonSettings = (object.pythonSettings !== undefined && object.pythonSettings !== null)
      ? PythonSettings.fromPartial(object.pythonSettings)
      : undefined;
    message.nodeSettings = (object.nodeSettings !== undefined && object.nodeSettings !== null)
      ? NodeSettings.fromPartial(object.nodeSettings)
      : undefined;
    message.dotnetSettings = (object.dotnetSettings !== undefined && object.dotnetSettings !== null)
      ? DotnetSettings.fromPartial(object.dotnetSettings)
      : undefined;
    message.rubySettings = (object.rubySettings !== undefined && object.rubySettings !== null)
      ? RubySettings.fromPartial(object.rubySettings)
      : undefined;
    message.goSettings = (object.goSettings !== undefined && object.goSettings !== null)
      ? GoSettings.fromPartial(object.goSettings)
      : undefined;
    return message;
  },
};

function createBasePublishing(): Publishing {
  return {
    methodSettings: [],
    newIssueUri: "",
    documentationUri: "",
    apiShortName: "",
    githubLabel: "",
    codeownerGithubTeams: [],
    docTagPrefix: "",
    organization: ClientLibraryOrganization.CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED,
    librarySettings: [],
    protoReferenceDocumentationUri: "",
    restReferenceDocumentationUri: "",
  };
}

export const Publishing: MessageFns<Publishing> = {
  encode(message: Publishing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.methodSettings) {
      MethodSettings.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.newIssueUri !== "") {
      writer.uint32(810).string(message.newIssueUri);
    }
    if (message.documentationUri !== "") {
      writer.uint32(818).string(message.documentationUri);
    }
    if (message.apiShortName !== "") {
      writer.uint32(826).string(message.apiShortName);
    }
    if (message.githubLabel !== "") {
      writer.uint32(834).string(message.githubLabel);
    }
    for (const v of message.codeownerGithubTeams) {
      writer.uint32(842).string(v!);
    }
    if (message.docTagPrefix !== "") {
      writer.uint32(850).string(message.docTagPrefix);
    }
    if (message.organization !== ClientLibraryOrganization.CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED) {
      writer.uint32(856).int32(clientLibraryOrganizationToNumber(message.organization));
    }
    for (const v of message.librarySettings) {
      ClientLibrarySettings.encode(v!, writer.uint32(874).fork()).join();
    }
    if (message.protoReferenceDocumentationUri !== "") {
      writer.uint32(882).string(message.protoReferenceDocumentationUri);
    }
    if (message.restReferenceDocumentationUri !== "") {
      writer.uint32(890).string(message.restReferenceDocumentationUri);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Publishing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePublishing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.methodSettings.push(MethodSettings.decode(reader, reader.uint32()));
          continue;
        }
        case 101: {
          if (tag !== 810) {
            break;
          }

          message.newIssueUri = reader.string();
          continue;
        }
        case 102: {
          if (tag !== 818) {
            break;
          }

          message.documentationUri = reader.string();
          continue;
        }
        case 103: {
          if (tag !== 826) {
            break;
          }

          message.apiShortName = reader.string();
          continue;
        }
        case 104: {
          if (tag !== 834) {
            break;
          }

          message.githubLabel = reader.string();
          continue;
        }
        case 105: {
          if (tag !== 842) {
            break;
          }

          message.codeownerGithubTeams.push(reader.string());
          continue;
        }
        case 106: {
          if (tag !== 850) {
            break;
          }

          message.docTagPrefix = reader.string();
          continue;
        }
        case 107: {
          if (tag !== 856) {
            break;
          }

          message.organization = clientLibraryOrganizationFromJSON(reader.int32());
          continue;
        }
        case 109: {
          if (tag !== 874) {
            break;
          }

          message.librarySettings.push(ClientLibrarySettings.decode(reader, reader.uint32()));
          continue;
        }
        case 110: {
          if (tag !== 882) {
            break;
          }

          message.protoReferenceDocumentationUri = reader.string();
          continue;
        }
        case 111: {
          if (tag !== 890) {
            break;
          }

          message.restReferenceDocumentationUri = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Publishing>): Publishing {
    return Publishing.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Publishing>): Publishing {
    const message = createBasePublishing();
    message.methodSettings = object.methodSettings?.map((e) => MethodSettings.fromPartial(e)) || [];
    message.newIssueUri = object.newIssueUri ?? "";
    message.documentationUri = object.documentationUri ?? "";
    message.apiShortName = object.apiShortName ?? "";
    message.githubLabel = object.githubLabel ?? "";
    message.codeownerGithubTeams = object.codeownerGithubTeams?.map((e) => e) || [];
    message.docTagPrefix = object.docTagPrefix ?? "";
    message.organization = object.organization ?? ClientLibraryOrganization.CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED;
    message.librarySettings = object.librarySettings?.map((e) => ClientLibrarySettings.fromPartial(e)) || [];
    message.protoReferenceDocumentationUri = object.protoReferenceDocumentationUri ?? "";
    message.restReferenceDocumentationUri = object.restReferenceDocumentationUri ?? "";
    return message;
  },
};

function createBaseJavaSettings(): JavaSettings {
  return { libraryPackage: "", serviceClassNames: {}, common: undefined };
}

export const JavaSettings: MessageFns<JavaSettings> = {
  encode(message: JavaSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.libraryPackage !== "") {
      writer.uint32(10).string(message.libraryPackage);
    }
    Object.entries(message.serviceClassNames).forEach(([key, value]) => {
      JavaSettings_ServiceClassNamesEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JavaSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJavaSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.libraryPackage = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = JavaSettings_ServiceClassNamesEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.serviceClassNames[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<JavaSettings>): JavaSettings {
    return JavaSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<JavaSettings>): JavaSettings {
    const message = createBaseJavaSettings();
    message.libraryPackage = object.libraryPackage ?? "";
    message.serviceClassNames = Object.entries(object.serviceClassNames ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    return message;
  },
};

function createBaseJavaSettings_ServiceClassNamesEntry(): JavaSettings_ServiceClassNamesEntry {
  return { key: "", value: "" };
}

export const JavaSettings_ServiceClassNamesEntry: MessageFns<JavaSettings_ServiceClassNamesEntry> = {
  encode(message: JavaSettings_ServiceClassNamesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JavaSettings_ServiceClassNamesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJavaSettings_ServiceClassNamesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<JavaSettings_ServiceClassNamesEntry>): JavaSettings_ServiceClassNamesEntry {
    return JavaSettings_ServiceClassNamesEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<JavaSettings_ServiceClassNamesEntry>): JavaSettings_ServiceClassNamesEntry {
    const message = createBaseJavaSettings_ServiceClassNamesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseCppSettings(): CppSettings {
  return { common: undefined };
}

export const CppSettings: MessageFns<CppSettings> = {
  encode(message: CppSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CppSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCppSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CppSettings>): CppSettings {
    return CppSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CppSettings>): CppSettings {
    const message = createBaseCppSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    return message;
  },
};

function createBasePhpSettings(): PhpSettings {
  return { common: undefined };
}

export const PhpSettings: MessageFns<PhpSettings> = {
  encode(message: PhpSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PhpSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePhpSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<PhpSettings>): PhpSettings {
    return PhpSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<PhpSettings>): PhpSettings {
    const message = createBasePhpSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    return message;
  },
};

function createBasePythonSettings(): PythonSettings {
  return { common: undefined, experimentalFeatures: undefined };
}

export const PythonSettings: MessageFns<PythonSettings> = {
  encode(message: PythonSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    if (message.experimentalFeatures !== undefined) {
      PythonSettings_ExperimentalFeatures.encode(message.experimentalFeatures, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PythonSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePythonSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.experimentalFeatures = PythonSettings_ExperimentalFeatures.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<PythonSettings>): PythonSettings {
    return PythonSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<PythonSettings>): PythonSettings {
    const message = createBasePythonSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    message.experimentalFeatures = (object.experimentalFeatures !== undefined && object.experimentalFeatures !== null)
      ? PythonSettings_ExperimentalFeatures.fromPartial(object.experimentalFeatures)
      : undefined;
    return message;
  },
};

function createBasePythonSettings_ExperimentalFeatures(): PythonSettings_ExperimentalFeatures {
  return { restAsyncIoEnabled: false, protobufPythonicTypesEnabled: false, unversionedPackageDisabled: false };
}

export const PythonSettings_ExperimentalFeatures: MessageFns<PythonSettings_ExperimentalFeatures> = {
  encode(message: PythonSettings_ExperimentalFeatures, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.restAsyncIoEnabled !== false) {
      writer.uint32(8).bool(message.restAsyncIoEnabled);
    }
    if (message.protobufPythonicTypesEnabled !== false) {
      writer.uint32(16).bool(message.protobufPythonicTypesEnabled);
    }
    if (message.unversionedPackageDisabled !== false) {
      writer.uint32(24).bool(message.unversionedPackageDisabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PythonSettings_ExperimentalFeatures {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePythonSettings_ExperimentalFeatures();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.restAsyncIoEnabled = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.protobufPythonicTypesEnabled = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.unversionedPackageDisabled = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<PythonSettings_ExperimentalFeatures>): PythonSettings_ExperimentalFeatures {
    return PythonSettings_ExperimentalFeatures.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<PythonSettings_ExperimentalFeatures>): PythonSettings_ExperimentalFeatures {
    const message = createBasePythonSettings_ExperimentalFeatures();
    message.restAsyncIoEnabled = object.restAsyncIoEnabled ?? false;
    message.protobufPythonicTypesEnabled = object.protobufPythonicTypesEnabled ?? false;
    message.unversionedPackageDisabled = object.unversionedPackageDisabled ?? false;
    return message;
  },
};

function createBaseNodeSettings(): NodeSettings {
  return { common: undefined };
}

export const NodeSettings: MessageFns<NodeSettings> = {
  encode(message: NodeSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NodeSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNodeSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<NodeSettings>): NodeSettings {
    return NodeSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<NodeSettings>): NodeSettings {
    const message = createBaseNodeSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    return message;
  },
};

function createBaseDotnetSettings(): DotnetSettings {
  return {
    common: undefined,
    renamedServices: {},
    renamedResources: {},
    ignoredResources: [],
    forcedNamespaceAliases: [],
    handwrittenSignatures: [],
  };
}

export const DotnetSettings: MessageFns<DotnetSettings> = {
  encode(message: DotnetSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    Object.entries(message.renamedServices).forEach(([key, value]) => {
      DotnetSettings_RenamedServicesEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.renamedResources).forEach(([key, value]) => {
      DotnetSettings_RenamedResourcesEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    for (const v of message.ignoredResources) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.forcedNamespaceAliases) {
      writer.uint32(42).string(v!);
    }
    for (const v of message.handwrittenSignatures) {
      writer.uint32(50).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DotnetSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDotnetSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = DotnetSettings_RenamedServicesEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.renamedServices[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = DotnetSettings_RenamedResourcesEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.renamedResources[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.ignoredResources.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.forcedNamespaceAliases.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.handwrittenSignatures.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DotnetSettings>): DotnetSettings {
    return DotnetSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DotnetSettings>): DotnetSettings {
    const message = createBaseDotnetSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    message.renamedServices = Object.entries(object.renamedServices ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    message.renamedResources = Object.entries(object.renamedResources ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    message.ignoredResources = object.ignoredResources?.map((e) => e) || [];
    message.forcedNamespaceAliases = object.forcedNamespaceAliases?.map((e) => e) || [];
    message.handwrittenSignatures = object.handwrittenSignatures?.map((e) => e) || [];
    return message;
  },
};

function createBaseDotnetSettings_RenamedServicesEntry(): DotnetSettings_RenamedServicesEntry {
  return { key: "", value: "" };
}

export const DotnetSettings_RenamedServicesEntry: MessageFns<DotnetSettings_RenamedServicesEntry> = {
  encode(message: DotnetSettings_RenamedServicesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DotnetSettings_RenamedServicesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDotnetSettings_RenamedServicesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DotnetSettings_RenamedServicesEntry>): DotnetSettings_RenamedServicesEntry {
    return DotnetSettings_RenamedServicesEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DotnetSettings_RenamedServicesEntry>): DotnetSettings_RenamedServicesEntry {
    const message = createBaseDotnetSettings_RenamedServicesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseDotnetSettings_RenamedResourcesEntry(): DotnetSettings_RenamedResourcesEntry {
  return { key: "", value: "" };
}

export const DotnetSettings_RenamedResourcesEntry: MessageFns<DotnetSettings_RenamedResourcesEntry> = {
  encode(message: DotnetSettings_RenamedResourcesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DotnetSettings_RenamedResourcesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDotnetSettings_RenamedResourcesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DotnetSettings_RenamedResourcesEntry>): DotnetSettings_RenamedResourcesEntry {
    return DotnetSettings_RenamedResourcesEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DotnetSettings_RenamedResourcesEntry>): DotnetSettings_RenamedResourcesEntry {
    const message = createBaseDotnetSettings_RenamedResourcesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseRubySettings(): RubySettings {
  return { common: undefined };
}

export const RubySettings: MessageFns<RubySettings> = {
  encode(message: RubySettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RubySettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRubySettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RubySettings>): RubySettings {
    return RubySettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RubySettings>): RubySettings {
    const message = createBaseRubySettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    return message;
  },
};

function createBaseGoSettings(): GoSettings {
  return { common: undefined, renamedServices: {} };
}

export const GoSettings: MessageFns<GoSettings> = {
  encode(message: GoSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.common !== undefined) {
      CommonLanguageSettings.encode(message.common, writer.uint32(10).fork()).join();
    }
    Object.entries(message.renamedServices).forEach(([key, value]) => {
      GoSettings_RenamedServicesEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.common = CommonLanguageSettings.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = GoSettings_RenamedServicesEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.renamedServices[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GoSettings>): GoSettings {
    return GoSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GoSettings>): GoSettings {
    const message = createBaseGoSettings();
    message.common = (object.common !== undefined && object.common !== null)
      ? CommonLanguageSettings.fromPartial(object.common)
      : undefined;
    message.renamedServices = Object.entries(object.renamedServices ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseGoSettings_RenamedServicesEntry(): GoSettings_RenamedServicesEntry {
  return { key: "", value: "" };
}

export const GoSettings_RenamedServicesEntry: MessageFns<GoSettings_RenamedServicesEntry> = {
  encode(message: GoSettings_RenamedServicesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoSettings_RenamedServicesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoSettings_RenamedServicesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GoSettings_RenamedServicesEntry>): GoSettings_RenamedServicesEntry {
    return GoSettings_RenamedServicesEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GoSettings_RenamedServicesEntry>): GoSettings_RenamedServicesEntry {
    const message = createBaseGoSettings_RenamedServicesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseMethodSettings(): MethodSettings {
  return { selector: "", longRunning: undefined, autoPopulatedFields: [] };
}

export const MethodSettings: MessageFns<MethodSettings> = {
  encode(message: MethodSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.selector !== "") {
      writer.uint32(10).string(message.selector);
    }
    if (message.longRunning !== undefined) {
      MethodSettings_LongRunning.encode(message.longRunning, writer.uint32(18).fork()).join();
    }
    for (const v of message.autoPopulatedFields) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MethodSettings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMethodSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.selector = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.longRunning = MethodSettings_LongRunning.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.autoPopulatedFields.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MethodSettings>): MethodSettings {
    return MethodSettings.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MethodSettings>): MethodSettings {
    const message = createBaseMethodSettings();
    message.selector = object.selector ?? "";
    message.longRunning = (object.longRunning !== undefined && object.longRunning !== null)
      ? MethodSettings_LongRunning.fromPartial(object.longRunning)
      : undefined;
    message.autoPopulatedFields = object.autoPopulatedFields?.map((e) => e) || [];
    return message;
  },
};

function createBaseMethodSettings_LongRunning(): MethodSettings_LongRunning {
  return { initialPollDelay: undefined, pollDelayMultiplier: 0, maxPollDelay: undefined, totalPollTimeout: undefined };
}

export const MethodSettings_LongRunning: MessageFns<MethodSettings_LongRunning> = {
  encode(message: MethodSettings_LongRunning, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.initialPollDelay !== undefined) {
      Duration.encode(message.initialPollDelay, writer.uint32(10).fork()).join();
    }
    if (message.pollDelayMultiplier !== 0) {
      writer.uint32(21).float(message.pollDelayMultiplier);
    }
    if (message.maxPollDelay !== undefined) {
      Duration.encode(message.maxPollDelay, writer.uint32(26).fork()).join();
    }
    if (message.totalPollTimeout !== undefined) {
      Duration.encode(message.totalPollTimeout, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MethodSettings_LongRunning {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMethodSettings_LongRunning();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.initialPollDelay = Duration.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 21) {
            break;
          }

          message.pollDelayMultiplier = reader.float();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.maxPollDelay = Duration.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.totalPollTimeout = Duration.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MethodSettings_LongRunning>): MethodSettings_LongRunning {
    return MethodSettings_LongRunning.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MethodSettings_LongRunning>): MethodSettings_LongRunning {
    const message = createBaseMethodSettings_LongRunning();
    message.initialPollDelay = (object.initialPollDelay !== undefined && object.initialPollDelay !== null)
      ? Duration.fromPartial(object.initialPollDelay)
      : undefined;
    message.pollDelayMultiplier = object.pollDelayMultiplier ?? 0;
    message.maxPollDelay = (object.maxPollDelay !== undefined && object.maxPollDelay !== null)
      ? Duration.fromPartial(object.maxPollDelay)
      : undefined;
    message.totalPollTimeout = (object.totalPollTimeout !== undefined && object.totalPollTimeout !== null)
      ? Duration.fromPartial(object.totalPollTimeout)
      : undefined;
    return message;
  },
};

function createBaseSelectiveGapicGeneration(): SelectiveGapicGeneration {
  return { methods: [], generateOmittedAsInternal: false };
}

export const SelectiveGapicGeneration: MessageFns<SelectiveGapicGeneration> = {
  encode(message: SelectiveGapicGeneration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.methods) {
      writer.uint32(10).string(v!);
    }
    if (message.generateOmittedAsInternal !== false) {
      writer.uint32(16).bool(message.generateOmittedAsInternal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SelectiveGapicGeneration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSelectiveGapicGeneration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.methods.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.generateOmittedAsInternal = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SelectiveGapicGeneration>): SelectiveGapicGeneration {
    return SelectiveGapicGeneration.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SelectiveGapicGeneration>): SelectiveGapicGeneration {
    const message = createBaseSelectiveGapicGeneration();
    message.methods = object.methods?.map((e) => e) || [];
    message.generateOmittedAsInternal = object.generateOmittedAsInternal ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
