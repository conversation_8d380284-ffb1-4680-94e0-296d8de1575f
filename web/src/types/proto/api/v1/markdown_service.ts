// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/markdown_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "memos.api.v1";

export enum NodeType {
  NODE_UNSPECIFIED = "NODE_UNSPECIFIED",
  /** LINE_BREAK - Block nodes. */
  LINE_BREAK = "LINE_BREAK",
  PARAGRAPH = "PARAGRAPH",
  CODE_BLOCK = "CODE_BLOCK",
  HEADING = "HEADING",
  HORIZONTAL_RULE = "HORIZONTAL_RULE",
  BLOCKQUOTE = "BLOCKQUOTE",
  LIST = "LIST",
  ORDERED_LIST_ITEM = "ORDERED_LIST_ITEM",
  UNORDERED_LIST_ITEM = "UNORDERED_LIST_ITEM",
  TASK_LIST_ITEM = "TASK_LIST_ITEM",
  MATH_BLOCK = "MATH_BLOCK",
  TABLE = "TABLE",
  EMBEDDED_CONTENT = "EMBEDDED_CONTENT",
  /** TEXT - Inline nodes. */
  TEXT = "TEXT",
  BOLD = "BOLD",
  ITALIC = "ITALIC",
  BOLD_ITALIC = "BOLD_ITALIC",
  CODE = "CODE",
  IMAGE = "IMAGE",
  LINK = "LINK",
  AUTO_LINK = "AUTO_LINK",
  TAG = "TAG",
  STRIKETHROUGH = "STRIKETHROUGH",
  ESCAPING_CHARACTER = "ESCAPING_CHARACTER",
  MATH = "MATH",
  HIGHLIGHT = "HIGHLIGHT",
  SUBSCRIPT = "SUBSCRIPT",
  SUPERSCRIPT = "SUPERSCRIPT",
  REFERENCED_CONTENT = "REFERENCED_CONTENT",
  SPOILER = "SPOILER",
  HTML_ELEMENT = "HTML_ELEMENT",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function nodeTypeFromJSON(object: any): NodeType {
  switch (object) {
    case 0:
    case "NODE_UNSPECIFIED":
      return NodeType.NODE_UNSPECIFIED;
    case 1:
    case "LINE_BREAK":
      return NodeType.LINE_BREAK;
    case 2:
    case "PARAGRAPH":
      return NodeType.PARAGRAPH;
    case 3:
    case "CODE_BLOCK":
      return NodeType.CODE_BLOCK;
    case 4:
    case "HEADING":
      return NodeType.HEADING;
    case 5:
    case "HORIZONTAL_RULE":
      return NodeType.HORIZONTAL_RULE;
    case 6:
    case "BLOCKQUOTE":
      return NodeType.BLOCKQUOTE;
    case 7:
    case "LIST":
      return NodeType.LIST;
    case 8:
    case "ORDERED_LIST_ITEM":
      return NodeType.ORDERED_LIST_ITEM;
    case 9:
    case "UNORDERED_LIST_ITEM":
      return NodeType.UNORDERED_LIST_ITEM;
    case 10:
    case "TASK_LIST_ITEM":
      return NodeType.TASK_LIST_ITEM;
    case 11:
    case "MATH_BLOCK":
      return NodeType.MATH_BLOCK;
    case 12:
    case "TABLE":
      return NodeType.TABLE;
    case 13:
    case "EMBEDDED_CONTENT":
      return NodeType.EMBEDDED_CONTENT;
    case 51:
    case "TEXT":
      return NodeType.TEXT;
    case 52:
    case "BOLD":
      return NodeType.BOLD;
    case 53:
    case "ITALIC":
      return NodeType.ITALIC;
    case 54:
    case "BOLD_ITALIC":
      return NodeType.BOLD_ITALIC;
    case 55:
    case "CODE":
      return NodeType.CODE;
    case 56:
    case "IMAGE":
      return NodeType.IMAGE;
    case 57:
    case "LINK":
      return NodeType.LINK;
    case 58:
    case "AUTO_LINK":
      return NodeType.AUTO_LINK;
    case 59:
    case "TAG":
      return NodeType.TAG;
    case 60:
    case "STRIKETHROUGH":
      return NodeType.STRIKETHROUGH;
    case 61:
    case "ESCAPING_CHARACTER":
      return NodeType.ESCAPING_CHARACTER;
    case 62:
    case "MATH":
      return NodeType.MATH;
    case 63:
    case "HIGHLIGHT":
      return NodeType.HIGHLIGHT;
    case 64:
    case "SUBSCRIPT":
      return NodeType.SUBSCRIPT;
    case 65:
    case "SUPERSCRIPT":
      return NodeType.SUPERSCRIPT;
    case 66:
    case "REFERENCED_CONTENT":
      return NodeType.REFERENCED_CONTENT;
    case 67:
    case "SPOILER":
      return NodeType.SPOILER;
    case 68:
    case "HTML_ELEMENT":
      return NodeType.HTML_ELEMENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NodeType.UNRECOGNIZED;
  }
}

export function nodeTypeToNumber(object: NodeType): number {
  switch (object) {
    case NodeType.NODE_UNSPECIFIED:
      return 0;
    case NodeType.LINE_BREAK:
      return 1;
    case NodeType.PARAGRAPH:
      return 2;
    case NodeType.CODE_BLOCK:
      return 3;
    case NodeType.HEADING:
      return 4;
    case NodeType.HORIZONTAL_RULE:
      return 5;
    case NodeType.BLOCKQUOTE:
      return 6;
    case NodeType.LIST:
      return 7;
    case NodeType.ORDERED_LIST_ITEM:
      return 8;
    case NodeType.UNORDERED_LIST_ITEM:
      return 9;
    case NodeType.TASK_LIST_ITEM:
      return 10;
    case NodeType.MATH_BLOCK:
      return 11;
    case NodeType.TABLE:
      return 12;
    case NodeType.EMBEDDED_CONTENT:
      return 13;
    case NodeType.TEXT:
      return 51;
    case NodeType.BOLD:
      return 52;
    case NodeType.ITALIC:
      return 53;
    case NodeType.BOLD_ITALIC:
      return 54;
    case NodeType.CODE:
      return 55;
    case NodeType.IMAGE:
      return 56;
    case NodeType.LINK:
      return 57;
    case NodeType.AUTO_LINK:
      return 58;
    case NodeType.TAG:
      return 59;
    case NodeType.STRIKETHROUGH:
      return 60;
    case NodeType.ESCAPING_CHARACTER:
      return 61;
    case NodeType.MATH:
      return 62;
    case NodeType.HIGHLIGHT:
      return 63;
    case NodeType.SUBSCRIPT:
      return 64;
    case NodeType.SUPERSCRIPT:
      return 65;
    case NodeType.REFERENCED_CONTENT:
      return 66;
    case NodeType.SPOILER:
      return 67;
    case NodeType.HTML_ELEMENT:
      return 68;
    case NodeType.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface ParseMarkdownRequest {
  markdown: string;
}

export interface ParseMarkdownResponse {
  nodes: Node[];
}

export interface RestoreMarkdownNodesRequest {
  nodes: Node[];
}

export interface RestoreMarkdownNodesResponse {
  markdown: string;
}

export interface StringifyMarkdownNodesRequest {
  nodes: Node[];
}

export interface StringifyMarkdownNodesResponse {
  plainText: string;
}

export interface GetLinkMetadataRequest {
  link: string;
}

export interface LinkMetadata {
  title: string;
  description: string;
  image: string;
}

export interface Node {
  type: NodeType;
  /** Block nodes. */
  lineBreakNode?: LineBreakNode | undefined;
  paragraphNode?: ParagraphNode | undefined;
  codeBlockNode?: CodeBlockNode | undefined;
  headingNode?: HeadingNode | undefined;
  horizontalRuleNode?: HorizontalRuleNode | undefined;
  blockquoteNode?: BlockquoteNode | undefined;
  listNode?: ListNode | undefined;
  orderedListItemNode?: OrderedListItemNode | undefined;
  unorderedListItemNode?: UnorderedListItemNode | undefined;
  taskListItemNode?: TaskListItemNode | undefined;
  mathBlockNode?: MathBlockNode | undefined;
  tableNode?: TableNode | undefined;
  embeddedContentNode?:
    | EmbeddedContentNode
    | undefined;
  /** Inline nodes. */
  textNode?: TextNode | undefined;
  boldNode?: BoldNode | undefined;
  italicNode?: ItalicNode | undefined;
  boldItalicNode?: BoldItalicNode | undefined;
  codeNode?: CodeNode | undefined;
  imageNode?: ImageNode | undefined;
  linkNode?: LinkNode | undefined;
  autoLinkNode?: AutoLinkNode | undefined;
  tagNode?: TagNode | undefined;
  strikethroughNode?: StrikethroughNode | undefined;
  escapingCharacterNode?: EscapingCharacterNode | undefined;
  mathNode?: MathNode | undefined;
  highlightNode?: HighlightNode | undefined;
  subscriptNode?: SubscriptNode | undefined;
  superscriptNode?: SuperscriptNode | undefined;
  referencedContentNode?: ReferencedContentNode | undefined;
  spoilerNode?: SpoilerNode | undefined;
  htmlElementNode?: HTMLElementNode | undefined;
}

export interface LineBreakNode {
}

export interface ParagraphNode {
  children: Node[];
}

export interface CodeBlockNode {
  language: string;
  content: string;
}

export interface HeadingNode {
  level: number;
  children: Node[];
}

export interface HorizontalRuleNode {
  symbol: string;
}

export interface BlockquoteNode {
  children: Node[];
}

export interface ListNode {
  kind: ListNode_Kind;
  indent: number;
  children: Node[];
}

export enum ListNode_Kind {
  KIND_UNSPECIFIED = "KIND_UNSPECIFIED",
  ORDERED = "ORDERED",
  UNORDERED = "UNORDERED",
  DESCRIPTION = "DESCRIPTION",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function listNode_KindFromJSON(object: any): ListNode_Kind {
  switch (object) {
    case 0:
    case "KIND_UNSPECIFIED":
      return ListNode_Kind.KIND_UNSPECIFIED;
    case 1:
    case "ORDERED":
      return ListNode_Kind.ORDERED;
    case 2:
    case "UNORDERED":
      return ListNode_Kind.UNORDERED;
    case 3:
    case "DESCRIPTION":
      return ListNode_Kind.DESCRIPTION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ListNode_Kind.UNRECOGNIZED;
  }
}

export function listNode_KindToNumber(object: ListNode_Kind): number {
  switch (object) {
    case ListNode_Kind.KIND_UNSPECIFIED:
      return 0;
    case ListNode_Kind.ORDERED:
      return 1;
    case ListNode_Kind.UNORDERED:
      return 2;
    case ListNode_Kind.DESCRIPTION:
      return 3;
    case ListNode_Kind.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface OrderedListItemNode {
  number: string;
  indent: number;
  children: Node[];
}

export interface UnorderedListItemNode {
  symbol: string;
  indent: number;
  children: Node[];
}

export interface TaskListItemNode {
  symbol: string;
  indent: number;
  complete: boolean;
  children: Node[];
}

export interface MathBlockNode {
  content: string;
}

export interface TableNode {
  header: Node[];
  delimiter: string[];
  rows: TableNode_Row[];
}

export interface TableNode_Row {
  cells: Node[];
}

export interface EmbeddedContentNode {
  resourceName: string;
  params: string;
}

export interface TextNode {
  content: string;
}

export interface BoldNode {
  symbol: string;
  children: Node[];
}

export interface ItalicNode {
  symbol: string;
  children: Node[];
}

export interface BoldItalicNode {
  symbol: string;
  content: string;
}

export interface CodeNode {
  content: string;
}

export interface ImageNode {
  altText: string;
  url: string;
}

export interface LinkNode {
  content: Node[];
  url: string;
}

export interface AutoLinkNode {
  url: string;
  isRawText: boolean;
}

export interface TagNode {
  content: string;
}

export interface StrikethroughNode {
  content: string;
}

export interface EscapingCharacterNode {
  symbol: string;
}

export interface MathNode {
  content: string;
}

export interface HighlightNode {
  content: string;
}

export interface SubscriptNode {
  content: string;
}

export interface SuperscriptNode {
  content: string;
}

export interface ReferencedContentNode {
  resourceName: string;
  params: string;
}

export interface SpoilerNode {
  content: string;
}

export interface HTMLElementNode {
  tagName: string;
  attributes: { [key: string]: string };
}

export interface HTMLElementNode_AttributesEntry {
  key: string;
  value: string;
}

function createBaseParseMarkdownRequest(): ParseMarkdownRequest {
  return { markdown: "" };
}

export const ParseMarkdownRequest: MessageFns<ParseMarkdownRequest> = {
  encode(message: ParseMarkdownRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.markdown !== "") {
      writer.uint32(10).string(message.markdown);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ParseMarkdownRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseParseMarkdownRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.markdown = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ParseMarkdownRequest>): ParseMarkdownRequest {
    return ParseMarkdownRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ParseMarkdownRequest>): ParseMarkdownRequest {
    const message = createBaseParseMarkdownRequest();
    message.markdown = object.markdown ?? "";
    return message;
  },
};

function createBaseParseMarkdownResponse(): ParseMarkdownResponse {
  return { nodes: [] };
}

export const ParseMarkdownResponse: MessageFns<ParseMarkdownResponse> = {
  encode(message: ParseMarkdownResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.nodes) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ParseMarkdownResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseParseMarkdownResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nodes.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ParseMarkdownResponse>): ParseMarkdownResponse {
    return ParseMarkdownResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ParseMarkdownResponse>): ParseMarkdownResponse {
    const message = createBaseParseMarkdownResponse();
    message.nodes = object.nodes?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRestoreMarkdownNodesRequest(): RestoreMarkdownNodesRequest {
  return { nodes: [] };
}

export const RestoreMarkdownNodesRequest: MessageFns<RestoreMarkdownNodesRequest> = {
  encode(message: RestoreMarkdownNodesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.nodes) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RestoreMarkdownNodesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRestoreMarkdownNodesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nodes.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RestoreMarkdownNodesRequest>): RestoreMarkdownNodesRequest {
    return RestoreMarkdownNodesRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RestoreMarkdownNodesRequest>): RestoreMarkdownNodesRequest {
    const message = createBaseRestoreMarkdownNodesRequest();
    message.nodes = object.nodes?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRestoreMarkdownNodesResponse(): RestoreMarkdownNodesResponse {
  return { markdown: "" };
}

export const RestoreMarkdownNodesResponse: MessageFns<RestoreMarkdownNodesResponse> = {
  encode(message: RestoreMarkdownNodesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.markdown !== "") {
      writer.uint32(10).string(message.markdown);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RestoreMarkdownNodesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRestoreMarkdownNodesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.markdown = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RestoreMarkdownNodesResponse>): RestoreMarkdownNodesResponse {
    return RestoreMarkdownNodesResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RestoreMarkdownNodesResponse>): RestoreMarkdownNodesResponse {
    const message = createBaseRestoreMarkdownNodesResponse();
    message.markdown = object.markdown ?? "";
    return message;
  },
};

function createBaseStringifyMarkdownNodesRequest(): StringifyMarkdownNodesRequest {
  return { nodes: [] };
}

export const StringifyMarkdownNodesRequest: MessageFns<StringifyMarkdownNodesRequest> = {
  encode(message: StringifyMarkdownNodesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.nodes) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringifyMarkdownNodesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringifyMarkdownNodesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nodes.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<StringifyMarkdownNodesRequest>): StringifyMarkdownNodesRequest {
    return StringifyMarkdownNodesRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<StringifyMarkdownNodesRequest>): StringifyMarkdownNodesRequest {
    const message = createBaseStringifyMarkdownNodesRequest();
    message.nodes = object.nodes?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseStringifyMarkdownNodesResponse(): StringifyMarkdownNodesResponse {
  return { plainText: "" };
}

export const StringifyMarkdownNodesResponse: MessageFns<StringifyMarkdownNodesResponse> = {
  encode(message: StringifyMarkdownNodesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.plainText !== "") {
      writer.uint32(10).string(message.plainText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringifyMarkdownNodesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringifyMarkdownNodesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.plainText = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<StringifyMarkdownNodesResponse>): StringifyMarkdownNodesResponse {
    return StringifyMarkdownNodesResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<StringifyMarkdownNodesResponse>): StringifyMarkdownNodesResponse {
    const message = createBaseStringifyMarkdownNodesResponse();
    message.plainText = object.plainText ?? "";
    return message;
  },
};

function createBaseGetLinkMetadataRequest(): GetLinkMetadataRequest {
  return { link: "" };
}

export const GetLinkMetadataRequest: MessageFns<GetLinkMetadataRequest> = {
  encode(message: GetLinkMetadataRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.link !== "") {
      writer.uint32(10).string(message.link);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetLinkMetadataRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetLinkMetadataRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.link = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetLinkMetadataRequest>): GetLinkMetadataRequest {
    return GetLinkMetadataRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetLinkMetadataRequest>): GetLinkMetadataRequest {
    const message = createBaseGetLinkMetadataRequest();
    message.link = object.link ?? "";
    return message;
  },
};

function createBaseLinkMetadata(): LinkMetadata {
  return { title: "", description: "", image: "" };
}

export const LinkMetadata: MessageFns<LinkMetadata> = {
  encode(message: LinkMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.image !== "") {
      writer.uint32(26).string(message.image);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LinkMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLinkMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.image = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<LinkMetadata>): LinkMetadata {
    return LinkMetadata.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<LinkMetadata>): LinkMetadata {
    const message = createBaseLinkMetadata();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.image = object.image ?? "";
    return message;
  },
};

function createBaseNode(): Node {
  return {
    type: NodeType.NODE_UNSPECIFIED,
    lineBreakNode: undefined,
    paragraphNode: undefined,
    codeBlockNode: undefined,
    headingNode: undefined,
    horizontalRuleNode: undefined,
    blockquoteNode: undefined,
    listNode: undefined,
    orderedListItemNode: undefined,
    unorderedListItemNode: undefined,
    taskListItemNode: undefined,
    mathBlockNode: undefined,
    tableNode: undefined,
    embeddedContentNode: undefined,
    textNode: undefined,
    boldNode: undefined,
    italicNode: undefined,
    boldItalicNode: undefined,
    codeNode: undefined,
    imageNode: undefined,
    linkNode: undefined,
    autoLinkNode: undefined,
    tagNode: undefined,
    strikethroughNode: undefined,
    escapingCharacterNode: undefined,
    mathNode: undefined,
    highlightNode: undefined,
    subscriptNode: undefined,
    superscriptNode: undefined,
    referencedContentNode: undefined,
    spoilerNode: undefined,
    htmlElementNode: undefined,
  };
}

export const Node: MessageFns<Node> = {
  encode(message: Node, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== NodeType.NODE_UNSPECIFIED) {
      writer.uint32(8).int32(nodeTypeToNumber(message.type));
    }
    if (message.lineBreakNode !== undefined) {
      LineBreakNode.encode(message.lineBreakNode, writer.uint32(90).fork()).join();
    }
    if (message.paragraphNode !== undefined) {
      ParagraphNode.encode(message.paragraphNode, writer.uint32(98).fork()).join();
    }
    if (message.codeBlockNode !== undefined) {
      CodeBlockNode.encode(message.codeBlockNode, writer.uint32(106).fork()).join();
    }
    if (message.headingNode !== undefined) {
      HeadingNode.encode(message.headingNode, writer.uint32(114).fork()).join();
    }
    if (message.horizontalRuleNode !== undefined) {
      HorizontalRuleNode.encode(message.horizontalRuleNode, writer.uint32(122).fork()).join();
    }
    if (message.blockquoteNode !== undefined) {
      BlockquoteNode.encode(message.blockquoteNode, writer.uint32(130).fork()).join();
    }
    if (message.listNode !== undefined) {
      ListNode.encode(message.listNode, writer.uint32(138).fork()).join();
    }
    if (message.orderedListItemNode !== undefined) {
      OrderedListItemNode.encode(message.orderedListItemNode, writer.uint32(146).fork()).join();
    }
    if (message.unorderedListItemNode !== undefined) {
      UnorderedListItemNode.encode(message.unorderedListItemNode, writer.uint32(154).fork()).join();
    }
    if (message.taskListItemNode !== undefined) {
      TaskListItemNode.encode(message.taskListItemNode, writer.uint32(162).fork()).join();
    }
    if (message.mathBlockNode !== undefined) {
      MathBlockNode.encode(message.mathBlockNode, writer.uint32(170).fork()).join();
    }
    if (message.tableNode !== undefined) {
      TableNode.encode(message.tableNode, writer.uint32(178).fork()).join();
    }
    if (message.embeddedContentNode !== undefined) {
      EmbeddedContentNode.encode(message.embeddedContentNode, writer.uint32(186).fork()).join();
    }
    if (message.textNode !== undefined) {
      TextNode.encode(message.textNode, writer.uint32(410).fork()).join();
    }
    if (message.boldNode !== undefined) {
      BoldNode.encode(message.boldNode, writer.uint32(418).fork()).join();
    }
    if (message.italicNode !== undefined) {
      ItalicNode.encode(message.italicNode, writer.uint32(426).fork()).join();
    }
    if (message.boldItalicNode !== undefined) {
      BoldItalicNode.encode(message.boldItalicNode, writer.uint32(434).fork()).join();
    }
    if (message.codeNode !== undefined) {
      CodeNode.encode(message.codeNode, writer.uint32(442).fork()).join();
    }
    if (message.imageNode !== undefined) {
      ImageNode.encode(message.imageNode, writer.uint32(450).fork()).join();
    }
    if (message.linkNode !== undefined) {
      LinkNode.encode(message.linkNode, writer.uint32(458).fork()).join();
    }
    if (message.autoLinkNode !== undefined) {
      AutoLinkNode.encode(message.autoLinkNode, writer.uint32(466).fork()).join();
    }
    if (message.tagNode !== undefined) {
      TagNode.encode(message.tagNode, writer.uint32(474).fork()).join();
    }
    if (message.strikethroughNode !== undefined) {
      StrikethroughNode.encode(message.strikethroughNode, writer.uint32(482).fork()).join();
    }
    if (message.escapingCharacterNode !== undefined) {
      EscapingCharacterNode.encode(message.escapingCharacterNode, writer.uint32(490).fork()).join();
    }
    if (message.mathNode !== undefined) {
      MathNode.encode(message.mathNode, writer.uint32(498).fork()).join();
    }
    if (message.highlightNode !== undefined) {
      HighlightNode.encode(message.highlightNode, writer.uint32(506).fork()).join();
    }
    if (message.subscriptNode !== undefined) {
      SubscriptNode.encode(message.subscriptNode, writer.uint32(514).fork()).join();
    }
    if (message.superscriptNode !== undefined) {
      SuperscriptNode.encode(message.superscriptNode, writer.uint32(522).fork()).join();
    }
    if (message.referencedContentNode !== undefined) {
      ReferencedContentNode.encode(message.referencedContentNode, writer.uint32(530).fork()).join();
    }
    if (message.spoilerNode !== undefined) {
      SpoilerNode.encode(message.spoilerNode, writer.uint32(538).fork()).join();
    }
    if (message.htmlElementNode !== undefined) {
      HTMLElementNode.encode(message.htmlElementNode, writer.uint32(546).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Node {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = nodeTypeFromJSON(reader.int32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.lineBreakNode = LineBreakNode.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.paragraphNode = ParagraphNode.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.codeBlockNode = CodeBlockNode.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.headingNode = HeadingNode.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.horizontalRuleNode = HorizontalRuleNode.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.blockquoteNode = BlockquoteNode.decode(reader, reader.uint32());
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.listNode = ListNode.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.orderedListItemNode = OrderedListItemNode.decode(reader, reader.uint32());
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.unorderedListItemNode = UnorderedListItemNode.decode(reader, reader.uint32());
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.taskListItemNode = TaskListItemNode.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.mathBlockNode = MathBlockNode.decode(reader, reader.uint32());
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.tableNode = TableNode.decode(reader, reader.uint32());
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.embeddedContentNode = EmbeddedContentNode.decode(reader, reader.uint32());
          continue;
        }
        case 51: {
          if (tag !== 410) {
            break;
          }

          message.textNode = TextNode.decode(reader, reader.uint32());
          continue;
        }
        case 52: {
          if (tag !== 418) {
            break;
          }

          message.boldNode = BoldNode.decode(reader, reader.uint32());
          continue;
        }
        case 53: {
          if (tag !== 426) {
            break;
          }

          message.italicNode = ItalicNode.decode(reader, reader.uint32());
          continue;
        }
        case 54: {
          if (tag !== 434) {
            break;
          }

          message.boldItalicNode = BoldItalicNode.decode(reader, reader.uint32());
          continue;
        }
        case 55: {
          if (tag !== 442) {
            break;
          }

          message.codeNode = CodeNode.decode(reader, reader.uint32());
          continue;
        }
        case 56: {
          if (tag !== 450) {
            break;
          }

          message.imageNode = ImageNode.decode(reader, reader.uint32());
          continue;
        }
        case 57: {
          if (tag !== 458) {
            break;
          }

          message.linkNode = LinkNode.decode(reader, reader.uint32());
          continue;
        }
        case 58: {
          if (tag !== 466) {
            break;
          }

          message.autoLinkNode = AutoLinkNode.decode(reader, reader.uint32());
          continue;
        }
        case 59: {
          if (tag !== 474) {
            break;
          }

          message.tagNode = TagNode.decode(reader, reader.uint32());
          continue;
        }
        case 60: {
          if (tag !== 482) {
            break;
          }

          message.strikethroughNode = StrikethroughNode.decode(reader, reader.uint32());
          continue;
        }
        case 61: {
          if (tag !== 490) {
            break;
          }

          message.escapingCharacterNode = EscapingCharacterNode.decode(reader, reader.uint32());
          continue;
        }
        case 62: {
          if (tag !== 498) {
            break;
          }

          message.mathNode = MathNode.decode(reader, reader.uint32());
          continue;
        }
        case 63: {
          if (tag !== 506) {
            break;
          }

          message.highlightNode = HighlightNode.decode(reader, reader.uint32());
          continue;
        }
        case 64: {
          if (tag !== 514) {
            break;
          }

          message.subscriptNode = SubscriptNode.decode(reader, reader.uint32());
          continue;
        }
        case 65: {
          if (tag !== 522) {
            break;
          }

          message.superscriptNode = SuperscriptNode.decode(reader, reader.uint32());
          continue;
        }
        case 66: {
          if (tag !== 530) {
            break;
          }

          message.referencedContentNode = ReferencedContentNode.decode(reader, reader.uint32());
          continue;
        }
        case 67: {
          if (tag !== 538) {
            break;
          }

          message.spoilerNode = SpoilerNode.decode(reader, reader.uint32());
          continue;
        }
        case 68: {
          if (tag !== 546) {
            break;
          }

          message.htmlElementNode = HTMLElementNode.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Node>): Node {
    return Node.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Node>): Node {
    const message = createBaseNode();
    message.type = object.type ?? NodeType.NODE_UNSPECIFIED;
    message.lineBreakNode = (object.lineBreakNode !== undefined && object.lineBreakNode !== null)
      ? LineBreakNode.fromPartial(object.lineBreakNode)
      : undefined;
    message.paragraphNode = (object.paragraphNode !== undefined && object.paragraphNode !== null)
      ? ParagraphNode.fromPartial(object.paragraphNode)
      : undefined;
    message.codeBlockNode = (object.codeBlockNode !== undefined && object.codeBlockNode !== null)
      ? CodeBlockNode.fromPartial(object.codeBlockNode)
      : undefined;
    message.headingNode = (object.headingNode !== undefined && object.headingNode !== null)
      ? HeadingNode.fromPartial(object.headingNode)
      : undefined;
    message.horizontalRuleNode = (object.horizontalRuleNode !== undefined && object.horizontalRuleNode !== null)
      ? HorizontalRuleNode.fromPartial(object.horizontalRuleNode)
      : undefined;
    message.blockquoteNode = (object.blockquoteNode !== undefined && object.blockquoteNode !== null)
      ? BlockquoteNode.fromPartial(object.blockquoteNode)
      : undefined;
    message.listNode = (object.listNode !== undefined && object.listNode !== null)
      ? ListNode.fromPartial(object.listNode)
      : undefined;
    message.orderedListItemNode = (object.orderedListItemNode !== undefined && object.orderedListItemNode !== null)
      ? OrderedListItemNode.fromPartial(object.orderedListItemNode)
      : undefined;
    message.unorderedListItemNode =
      (object.unorderedListItemNode !== undefined && object.unorderedListItemNode !== null)
        ? UnorderedListItemNode.fromPartial(object.unorderedListItemNode)
        : undefined;
    message.taskListItemNode = (object.taskListItemNode !== undefined && object.taskListItemNode !== null)
      ? TaskListItemNode.fromPartial(object.taskListItemNode)
      : undefined;
    message.mathBlockNode = (object.mathBlockNode !== undefined && object.mathBlockNode !== null)
      ? MathBlockNode.fromPartial(object.mathBlockNode)
      : undefined;
    message.tableNode = (object.tableNode !== undefined && object.tableNode !== null)
      ? TableNode.fromPartial(object.tableNode)
      : undefined;
    message.embeddedContentNode = (object.embeddedContentNode !== undefined && object.embeddedContentNode !== null)
      ? EmbeddedContentNode.fromPartial(object.embeddedContentNode)
      : undefined;
    message.textNode = (object.textNode !== undefined && object.textNode !== null)
      ? TextNode.fromPartial(object.textNode)
      : undefined;
    message.boldNode = (object.boldNode !== undefined && object.boldNode !== null)
      ? BoldNode.fromPartial(object.boldNode)
      : undefined;
    message.italicNode = (object.italicNode !== undefined && object.italicNode !== null)
      ? ItalicNode.fromPartial(object.italicNode)
      : undefined;
    message.boldItalicNode = (object.boldItalicNode !== undefined && object.boldItalicNode !== null)
      ? BoldItalicNode.fromPartial(object.boldItalicNode)
      : undefined;
    message.codeNode = (object.codeNode !== undefined && object.codeNode !== null)
      ? CodeNode.fromPartial(object.codeNode)
      : undefined;
    message.imageNode = (object.imageNode !== undefined && object.imageNode !== null)
      ? ImageNode.fromPartial(object.imageNode)
      : undefined;
    message.linkNode = (object.linkNode !== undefined && object.linkNode !== null)
      ? LinkNode.fromPartial(object.linkNode)
      : undefined;
    message.autoLinkNode = (object.autoLinkNode !== undefined && object.autoLinkNode !== null)
      ? AutoLinkNode.fromPartial(object.autoLinkNode)
      : undefined;
    message.tagNode = (object.tagNode !== undefined && object.tagNode !== null)
      ? TagNode.fromPartial(object.tagNode)
      : undefined;
    message.strikethroughNode = (object.strikethroughNode !== undefined && object.strikethroughNode !== null)
      ? StrikethroughNode.fromPartial(object.strikethroughNode)
      : undefined;
    message.escapingCharacterNode =
      (object.escapingCharacterNode !== undefined && object.escapingCharacterNode !== null)
        ? EscapingCharacterNode.fromPartial(object.escapingCharacterNode)
        : undefined;
    message.mathNode = (object.mathNode !== undefined && object.mathNode !== null)
      ? MathNode.fromPartial(object.mathNode)
      : undefined;
    message.highlightNode = (object.highlightNode !== undefined && object.highlightNode !== null)
      ? HighlightNode.fromPartial(object.highlightNode)
      : undefined;
    message.subscriptNode = (object.subscriptNode !== undefined && object.subscriptNode !== null)
      ? SubscriptNode.fromPartial(object.subscriptNode)
      : undefined;
    message.superscriptNode = (object.superscriptNode !== undefined && object.superscriptNode !== null)
      ? SuperscriptNode.fromPartial(object.superscriptNode)
      : undefined;
    message.referencedContentNode =
      (object.referencedContentNode !== undefined && object.referencedContentNode !== null)
        ? ReferencedContentNode.fromPartial(object.referencedContentNode)
        : undefined;
    message.spoilerNode = (object.spoilerNode !== undefined && object.spoilerNode !== null)
      ? SpoilerNode.fromPartial(object.spoilerNode)
      : undefined;
    message.htmlElementNode = (object.htmlElementNode !== undefined && object.htmlElementNode !== null)
      ? HTMLElementNode.fromPartial(object.htmlElementNode)
      : undefined;
    return message;
  },
};

function createBaseLineBreakNode(): LineBreakNode {
  return {};
}

export const LineBreakNode: MessageFns<LineBreakNode> = {
  encode(_: LineBreakNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LineBreakNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLineBreakNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<LineBreakNode>): LineBreakNode {
    return LineBreakNode.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<LineBreakNode>): LineBreakNode {
    const message = createBaseLineBreakNode();
    return message;
  },
};

function createBaseParagraphNode(): ParagraphNode {
  return { children: [] };
}

export const ParagraphNode: MessageFns<ParagraphNode> = {
  encode(message: ParagraphNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ParagraphNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseParagraphNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ParagraphNode>): ParagraphNode {
    return ParagraphNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ParagraphNode>): ParagraphNode {
    const message = createBaseParagraphNode();
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCodeBlockNode(): CodeBlockNode {
  return { language: "", content: "" };
}

export const CodeBlockNode: MessageFns<CodeBlockNode> = {
  encode(message: CodeBlockNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.language !== "") {
      writer.uint32(10).string(message.language);
    }
    if (message.content !== "") {
      writer.uint32(18).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CodeBlockNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCodeBlockNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.language = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CodeBlockNode>): CodeBlockNode {
    return CodeBlockNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CodeBlockNode>): CodeBlockNode {
    const message = createBaseCodeBlockNode();
    message.language = object.language ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseHeadingNode(): HeadingNode {
  return { level: 0, children: [] };
}

export const HeadingNode: MessageFns<HeadingNode> = {
  encode(message: HeadingNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int32(message.level);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeadingNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeadingNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<HeadingNode>): HeadingNode {
    return HeadingNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<HeadingNode>): HeadingNode {
    const message = createBaseHeadingNode();
    message.level = object.level ?? 0;
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseHorizontalRuleNode(): HorizontalRuleNode {
  return { symbol: "" };
}

export const HorizontalRuleNode: MessageFns<HorizontalRuleNode> = {
  encode(message: HorizontalRuleNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HorizontalRuleNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHorizontalRuleNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<HorizontalRuleNode>): HorizontalRuleNode {
    return HorizontalRuleNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<HorizontalRuleNode>): HorizontalRuleNode {
    const message = createBaseHorizontalRuleNode();
    message.symbol = object.symbol ?? "";
    return message;
  },
};

function createBaseBlockquoteNode(): BlockquoteNode {
  return { children: [] };
}

export const BlockquoteNode: MessageFns<BlockquoteNode> = {
  encode(message: BlockquoteNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BlockquoteNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlockquoteNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<BlockquoteNode>): BlockquoteNode {
    return BlockquoteNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BlockquoteNode>): BlockquoteNode {
    const message = createBaseBlockquoteNode();
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListNode(): ListNode {
  return { kind: ListNode_Kind.KIND_UNSPECIFIED, indent: 0, children: [] };
}

export const ListNode: MessageFns<ListNode> = {
  encode(message: ListNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.kind !== ListNode_Kind.KIND_UNSPECIFIED) {
      writer.uint32(8).int32(listNode_KindToNumber(message.kind));
    }
    if (message.indent !== 0) {
      writer.uint32(16).int32(message.indent);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.kind = listNode_KindFromJSON(reader.int32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.indent = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListNode>): ListNode {
    return ListNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListNode>): ListNode {
    const message = createBaseListNode();
    message.kind = object.kind ?? ListNode_Kind.KIND_UNSPECIFIED;
    message.indent = object.indent ?? 0;
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOrderedListItemNode(): OrderedListItemNode {
  return { number: "", indent: 0, children: [] };
}

export const OrderedListItemNode: MessageFns<OrderedListItemNode> = {
  encode(message: OrderedListItemNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.number !== "") {
      writer.uint32(10).string(message.number);
    }
    if (message.indent !== 0) {
      writer.uint32(16).int32(message.indent);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OrderedListItemNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOrderedListItemNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.number = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.indent = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<OrderedListItemNode>): OrderedListItemNode {
    return OrderedListItemNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<OrderedListItemNode>): OrderedListItemNode {
    const message = createBaseOrderedListItemNode();
    message.number = object.number ?? "";
    message.indent = object.indent ?? 0;
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUnorderedListItemNode(): UnorderedListItemNode {
  return { symbol: "", indent: 0, children: [] };
}

export const UnorderedListItemNode: MessageFns<UnorderedListItemNode> = {
  encode(message: UnorderedListItemNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    if (message.indent !== 0) {
      writer.uint32(16).int32(message.indent);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UnorderedListItemNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUnorderedListItemNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.indent = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UnorderedListItemNode>): UnorderedListItemNode {
    return UnorderedListItemNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UnorderedListItemNode>): UnorderedListItemNode {
    const message = createBaseUnorderedListItemNode();
    message.symbol = object.symbol ?? "";
    message.indent = object.indent ?? 0;
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTaskListItemNode(): TaskListItemNode {
  return { symbol: "", indent: 0, complete: false, children: [] };
}

export const TaskListItemNode: MessageFns<TaskListItemNode> = {
  encode(message: TaskListItemNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    if (message.indent !== 0) {
      writer.uint32(16).int32(message.indent);
    }
    if (message.complete !== false) {
      writer.uint32(24).bool(message.complete);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TaskListItemNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTaskListItemNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.indent = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.complete = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<TaskListItemNode>): TaskListItemNode {
    return TaskListItemNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TaskListItemNode>): TaskListItemNode {
    const message = createBaseTaskListItemNode();
    message.symbol = object.symbol ?? "";
    message.indent = object.indent ?? 0;
    message.complete = object.complete ?? false;
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMathBlockNode(): MathBlockNode {
  return { content: "" };
}

export const MathBlockNode: MessageFns<MathBlockNode> = {
  encode(message: MathBlockNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MathBlockNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMathBlockNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MathBlockNode>): MathBlockNode {
    return MathBlockNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MathBlockNode>): MathBlockNode {
    const message = createBaseMathBlockNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseTableNode(): TableNode {
  return { header: [], delimiter: [], rows: [] };
}

export const TableNode: MessageFns<TableNode> = {
  encode(message: TableNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.header) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.delimiter) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.rows) {
      TableNode_Row.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TableNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTableNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header.push(Node.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.delimiter.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rows.push(TableNode_Row.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<TableNode>): TableNode {
    return TableNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TableNode>): TableNode {
    const message = createBaseTableNode();
    message.header = object.header?.map((e) => Node.fromPartial(e)) || [];
    message.delimiter = object.delimiter?.map((e) => e) || [];
    message.rows = object.rows?.map((e) => TableNode_Row.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTableNode_Row(): TableNode_Row {
  return { cells: [] };
}

export const TableNode_Row: MessageFns<TableNode_Row> = {
  encode(message: TableNode_Row, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cells) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TableNode_Row {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTableNode_Row();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cells.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<TableNode_Row>): TableNode_Row {
    return TableNode_Row.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TableNode_Row>): TableNode_Row {
    const message = createBaseTableNode_Row();
    message.cells = object.cells?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseEmbeddedContentNode(): EmbeddedContentNode {
  return { resourceName: "", params: "" };
}

export const EmbeddedContentNode: MessageFns<EmbeddedContentNode> = {
  encode(message: EmbeddedContentNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.resourceName !== "") {
      writer.uint32(10).string(message.resourceName);
    }
    if (message.params !== "") {
      writer.uint32(18).string(message.params);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmbeddedContentNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmbeddedContentNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resourceName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.params = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<EmbeddedContentNode>): EmbeddedContentNode {
    return EmbeddedContentNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EmbeddedContentNode>): EmbeddedContentNode {
    const message = createBaseEmbeddedContentNode();
    message.resourceName = object.resourceName ?? "";
    message.params = object.params ?? "";
    return message;
  },
};

function createBaseTextNode(): TextNode {
  return { content: "" };
}

export const TextNode: MessageFns<TextNode> = {
  encode(message: TextNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<TextNode>): TextNode {
    return TextNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TextNode>): TextNode {
    const message = createBaseTextNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseBoldNode(): BoldNode {
  return { symbol: "", children: [] };
}

export const BoldNode: MessageFns<BoldNode> = {
  encode(message: BoldNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BoldNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoldNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<BoldNode>): BoldNode {
    return BoldNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BoldNode>): BoldNode {
    const message = createBaseBoldNode();
    message.symbol = object.symbol ?? "";
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseItalicNode(): ItalicNode {
  return { symbol: "", children: [] };
}

export const ItalicNode: MessageFns<ItalicNode> = {
  encode(message: ItalicNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    for (const v of message.children) {
      Node.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ItalicNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseItalicNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.children.push(Node.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ItalicNode>): ItalicNode {
    return ItalicNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ItalicNode>): ItalicNode {
    const message = createBaseItalicNode();
    message.symbol = object.symbol ?? "";
    message.children = object.children?.map((e) => Node.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBoldItalicNode(): BoldItalicNode {
  return { symbol: "", content: "" };
}

export const BoldItalicNode: MessageFns<BoldItalicNode> = {
  encode(message: BoldItalicNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    if (message.content !== "") {
      writer.uint32(18).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BoldItalicNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoldItalicNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<BoldItalicNode>): BoldItalicNode {
    return BoldItalicNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BoldItalicNode>): BoldItalicNode {
    const message = createBaseBoldItalicNode();
    message.symbol = object.symbol ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseCodeNode(): CodeNode {
  return { content: "" };
}

export const CodeNode: MessageFns<CodeNode> = {
  encode(message: CodeNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CodeNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCodeNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CodeNode>): CodeNode {
    return CodeNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CodeNode>): CodeNode {
    const message = createBaseCodeNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseImageNode(): ImageNode {
  return { altText: "", url: "" };
}

export const ImageNode: MessageFns<ImageNode> = {
  encode(message: ImageNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.altText !== "") {
      writer.uint32(10).string(message.altText);
    }
    if (message.url !== "") {
      writer.uint32(18).string(message.url);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ImageNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseImageNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.altText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.url = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ImageNode>): ImageNode {
    return ImageNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ImageNode>): ImageNode {
    const message = createBaseImageNode();
    message.altText = object.altText ?? "";
    message.url = object.url ?? "";
    return message;
  },
};

function createBaseLinkNode(): LinkNode {
  return { content: [], url: "" };
}

export const LinkNode: MessageFns<LinkNode> = {
  encode(message: LinkNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.content) {
      Node.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.url !== "") {
      writer.uint32(18).string(message.url);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LinkNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLinkNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content.push(Node.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.url = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<LinkNode>): LinkNode {
    return LinkNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<LinkNode>): LinkNode {
    const message = createBaseLinkNode();
    message.content = object.content?.map((e) => Node.fromPartial(e)) || [];
    message.url = object.url ?? "";
    return message;
  },
};

function createBaseAutoLinkNode(): AutoLinkNode {
  return { url: "", isRawText: false };
}

export const AutoLinkNode: MessageFns<AutoLinkNode> = {
  encode(message: AutoLinkNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.url !== "") {
      writer.uint32(10).string(message.url);
    }
    if (message.isRawText !== false) {
      writer.uint32(16).bool(message.isRawText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AutoLinkNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutoLinkNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.url = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isRawText = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<AutoLinkNode>): AutoLinkNode {
    return AutoLinkNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<AutoLinkNode>): AutoLinkNode {
    const message = createBaseAutoLinkNode();
    message.url = object.url ?? "";
    message.isRawText = object.isRawText ?? false;
    return message;
  },
};

function createBaseTagNode(): TagNode {
  return { content: "" };
}

export const TagNode: MessageFns<TagNode> = {
  encode(message: TagNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TagNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTagNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<TagNode>): TagNode {
    return TagNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TagNode>): TagNode {
    const message = createBaseTagNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseStrikethroughNode(): StrikethroughNode {
  return { content: "" };
}

export const StrikethroughNode: MessageFns<StrikethroughNode> = {
  encode(message: StrikethroughNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StrikethroughNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStrikethroughNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<StrikethroughNode>): StrikethroughNode {
    return StrikethroughNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<StrikethroughNode>): StrikethroughNode {
    const message = createBaseStrikethroughNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseEscapingCharacterNode(): EscapingCharacterNode {
  return { symbol: "" };
}

export const EscapingCharacterNode: MessageFns<EscapingCharacterNode> = {
  encode(message: EscapingCharacterNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.symbol !== "") {
      writer.uint32(10).string(message.symbol);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EscapingCharacterNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEscapingCharacterNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.symbol = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<EscapingCharacterNode>): EscapingCharacterNode {
    return EscapingCharacterNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EscapingCharacterNode>): EscapingCharacterNode {
    const message = createBaseEscapingCharacterNode();
    message.symbol = object.symbol ?? "";
    return message;
  },
};

function createBaseMathNode(): MathNode {
  return { content: "" };
}

export const MathNode: MessageFns<MathNode> = {
  encode(message: MathNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MathNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMathNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MathNode>): MathNode {
    return MathNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MathNode>): MathNode {
    const message = createBaseMathNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseHighlightNode(): HighlightNode {
  return { content: "" };
}

export const HighlightNode: MessageFns<HighlightNode> = {
  encode(message: HighlightNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HighlightNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHighlightNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<HighlightNode>): HighlightNode {
    return HighlightNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<HighlightNode>): HighlightNode {
    const message = createBaseHighlightNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseSubscriptNode(): SubscriptNode {
  return { content: "" };
}

export const SubscriptNode: MessageFns<SubscriptNode> = {
  encode(message: SubscriptNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscriptNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscriptNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SubscriptNode>): SubscriptNode {
    return SubscriptNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SubscriptNode>): SubscriptNode {
    const message = createBaseSubscriptNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseSuperscriptNode(): SuperscriptNode {
  return { content: "" };
}

export const SuperscriptNode: MessageFns<SuperscriptNode> = {
  encode(message: SuperscriptNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SuperscriptNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSuperscriptNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SuperscriptNode>): SuperscriptNode {
    return SuperscriptNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SuperscriptNode>): SuperscriptNode {
    const message = createBaseSuperscriptNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseReferencedContentNode(): ReferencedContentNode {
  return { resourceName: "", params: "" };
}

export const ReferencedContentNode: MessageFns<ReferencedContentNode> = {
  encode(message: ReferencedContentNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.resourceName !== "") {
      writer.uint32(10).string(message.resourceName);
    }
    if (message.params !== "") {
      writer.uint32(18).string(message.params);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferencedContentNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferencedContentNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resourceName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.params = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ReferencedContentNode>): ReferencedContentNode {
    return ReferencedContentNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ReferencedContentNode>): ReferencedContentNode {
    const message = createBaseReferencedContentNode();
    message.resourceName = object.resourceName ?? "";
    message.params = object.params ?? "";
    return message;
  },
};

function createBaseSpoilerNode(): SpoilerNode {
  return { content: "" };
}

export const SpoilerNode: MessageFns<SpoilerNode> = {
  encode(message: SpoilerNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.content !== "") {
      writer.uint32(10).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SpoilerNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSpoilerNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SpoilerNode>): SpoilerNode {
    return SpoilerNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SpoilerNode>): SpoilerNode {
    const message = createBaseSpoilerNode();
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseHTMLElementNode(): HTMLElementNode {
  return { tagName: "", attributes: {} };
}

export const HTMLElementNode: MessageFns<HTMLElementNode> = {
  encode(message: HTMLElementNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tagName !== "") {
      writer.uint32(10).string(message.tagName);
    }
    Object.entries(message.attributes).forEach(([key, value]) => {
      HTMLElementNode_AttributesEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HTMLElementNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHTMLElementNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tagName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = HTMLElementNode_AttributesEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.attributes[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<HTMLElementNode>): HTMLElementNode {
    return HTMLElementNode.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<HTMLElementNode>): HTMLElementNode {
    const message = createBaseHTMLElementNode();
    message.tagName = object.tagName ?? "";
    message.attributes = Object.entries(object.attributes ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseHTMLElementNode_AttributesEntry(): HTMLElementNode_AttributesEntry {
  return { key: "", value: "" };
}

export const HTMLElementNode_AttributesEntry: MessageFns<HTMLElementNode_AttributesEntry> = {
  encode(message: HTMLElementNode_AttributesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HTMLElementNode_AttributesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHTMLElementNode_AttributesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<HTMLElementNode_AttributesEntry>): HTMLElementNode_AttributesEntry {
    return HTMLElementNode_AttributesEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<HTMLElementNode_AttributesEntry>): HTMLElementNode_AttributesEntry {
    const message = createBaseHTMLElementNode_AttributesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

export type MarkdownServiceDefinition = typeof MarkdownServiceDefinition;
export const MarkdownServiceDefinition = {
  name: "MarkdownService",
  fullName: "memos.api.v1.MarkdownService",
  methods: {
    /** ParseMarkdown parses the given markdown content and returns a list of nodes. */
    parseMarkdown: {
      name: "ParseMarkdown",
      requestType: ParseMarkdownRequest,
      requestStream: false,
      responseType: ParseMarkdownResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              27,
              58,
              1,
              42,
              34,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              97,
              114,
              107,
              100,
              111,
              119,
              110,
              58,
              112,
              97,
              114,
              115,
              101,
            ]),
          ],
        },
      },
    },
    /** RestoreMarkdownNodes restores the given nodes to markdown content. */
    restoreMarkdownNodes: {
      name: "RestoreMarkdownNodes",
      requestType: RestoreMarkdownNodesRequest,
      requestStream: false,
      responseType: RestoreMarkdownNodesResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              34,
              58,
              1,
              42,
              34,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              97,
              114,
              107,
              100,
              111,
              119,
              110,
              47,
              110,
              111,
              100,
              101,
              58,
              114,
              101,
              115,
              116,
              111,
              114,
              101,
            ]),
          ],
        },
      },
    },
    /** StringifyMarkdownNodes stringify the given nodes to plain text content. */
    stringifyMarkdownNodes: {
      name: "StringifyMarkdownNodes",
      requestType: StringifyMarkdownNodesRequest,
      requestStream: false,
      responseType: StringifyMarkdownNodesResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              36,
              58,
              1,
              42,
              34,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              97,
              114,
              107,
              100,
              111,
              119,
              110,
              47,
              110,
              111,
              100,
              101,
              58,
              115,
              116,
              114,
              105,
              110,
              103,
              105,
              102,
              121,
            ]),
          ],
        },
      },
    },
    /** GetLinkMetadata returns metadata for a given link. */
    getLinkMetadata: {
      name: "GetLinkMetadata",
      requestType: GetLinkMetadataRequest,
      requestStream: false,
      responseType: LinkMetadata,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              97,
              114,
              107,
              100,
              111,
              119,
              110,
              47,
              108,
              105,
              110,
              107,
              58,
              109,
              101,
              116,
              97,
              100,
              97,
              116,
              97,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
