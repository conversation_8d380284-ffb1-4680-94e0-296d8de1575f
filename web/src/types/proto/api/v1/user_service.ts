// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/user_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { HttpBody } from "../../google/api/httpbody";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";
import { State, stateFromJSON, stateToNumber } from "./common";

export const protobufPackage = "memos.api.v1";

export interface User {
  /**
   * The name of the user.
   * Format: users/{id}, id is the system generated auto-incremented id.
   */
  name: string;
  role: User_Role;
  username: string;
  email: string;
  nickname: string;
  avatarUrl: string;
  description: string;
  password: string;
  state: State;
  createTime?: Date | undefined;
  updateTime?: Date | undefined;
}

export enum User_Role {
  ROLE_UNSPECIFIED = "ROLE_UNSPECIFIED",
  HOST = "HOST",
  ADMIN = "ADMIN",
  USER = "USER",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function user_RoleFromJSON(object: any): User_Role {
  switch (object) {
    case 0:
    case "ROLE_UNSPECIFIED":
      return User_Role.ROLE_UNSPECIFIED;
    case 1:
    case "HOST":
      return User_Role.HOST;
    case 2:
    case "ADMIN":
      return User_Role.ADMIN;
    case 3:
    case "USER":
      return User_Role.USER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return User_Role.UNRECOGNIZED;
  }
}

export function user_RoleToNumber(object: User_Role): number {
  switch (object) {
    case User_Role.ROLE_UNSPECIFIED:
      return 0;
    case User_Role.HOST:
      return 1;
    case User_Role.ADMIN:
      return 2;
    case User_Role.USER:
      return 3;
    case User_Role.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface ListUsersRequest {
}

export interface ListUsersResponse {
  users: User[];
}

export interface GetUserRequest {
  /** The name of the user. */
  name: string;
}

export interface GetUserByUsernameRequest {
  /** The username of the user. */
  username: string;
}

export interface GetUserAvatarBinaryRequest {
  /** The name of the user. */
  name: string;
  /** The raw HTTP body is bound to this field. */
  httpBody?: HttpBody | undefined;
}

export interface CreateUserRequest {
  user?: User | undefined;
}

export interface UpdateUserRequest {
  user?: User | undefined;
  updateMask?: string[] | undefined;
}

export interface DeleteUserRequest {
  /** The name of the user. */
  name: string;
}

export interface UserStats {
  /** The name of the user. */
  name: string;
  /**
   * The timestamps when the memos were displayed.
   * We should return raw data to the client, and let the client format the data with the user's timezone.
   */
  memoDisplayTimestamps: Date[];
  /** The stats of memo types. */
  memoTypeStats?:
    | UserStats_MemoTypeStats
    | undefined;
  /**
   * The count of tags.
   * Format: "tag1": 1, "tag2": 2
   */
  tagCount: { [key: string]: number };
  /** The pinned memos of the user. */
  pinnedMemos: string[];
  totalMemoCount: number;
}

export interface UserStats_TagCountEntry {
  key: string;
  value: number;
}

export interface UserStats_MemoTypeStats {
  linkCount: number;
  codeCount: number;
  todoCount: number;
  undoCount: number;
}

export interface ListAllUserStatsRequest {
}

export interface ListAllUserStatsResponse {
  userStats: UserStats[];
}

export interface GetUserStatsRequest {
  /** The name of the user. */
  name: string;
}

export interface UserSetting {
  /** The name of the user. */
  name: string;
  /** The preferred locale of the user. */
  locale: string;
  /** The preferred appearance of the user. */
  appearance: string;
  /** The default visibility of the memo. */
  memoVisibility: string;
}

export interface GetUserSettingRequest {
  /** The name of the user. */
  name: string;
}

export interface UpdateUserSettingRequest {
  setting?: UserSetting | undefined;
  updateMask?: string[] | undefined;
}

export interface UserAccessToken {
  accessToken: string;
  description: string;
  issuedAt?: Date | undefined;
  expiresAt?: Date | undefined;
}

export interface ListUserAccessTokensRequest {
  /** The name of the user. */
  name: string;
}

export interface ListUserAccessTokensResponse {
  accessTokens: UserAccessToken[];
}

export interface CreateUserAccessTokenRequest {
  /** The name of the user. */
  name: string;
  description: string;
  expiresAt?: Date | undefined;
}

export interface DeleteUserAccessTokenRequest {
  /** The name of the user. */
  name: string;
  /** access_token is the access token to delete. */
  accessToken: string;
}

export interface Shortcut {
  id: string;
  title: string;
  filter: string;
}

export interface ListShortcutsRequest {
  /** The name of the user. */
  parent: string;
}

export interface ListShortcutsResponse {
  shortcuts: Shortcut[];
}

export interface CreateShortcutRequest {
  /** The name of the user. */
  parent: string;
  shortcut?: Shortcut | undefined;
  validateOnly: boolean;
}

export interface UpdateShortcutRequest {
  /** The name of the user. */
  parent: string;
  shortcut?: Shortcut | undefined;
  updateMask?: string[] | undefined;
}

export interface DeleteShortcutRequest {
  /** The name of the user. */
  parent: string;
  /** The id of the shortcut. */
  id: string;
}

function createBaseUser(): User {
  return {
    name: "",
    role: User_Role.ROLE_UNSPECIFIED,
    username: "",
    email: "",
    nickname: "",
    avatarUrl: "",
    description: "",
    password: "",
    state: State.STATE_UNSPECIFIED,
    createTime: undefined,
    updateTime: undefined,
  };
}

export const User: MessageFns<User> = {
  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.role !== User_Role.ROLE_UNSPECIFIED) {
      writer.uint32(24).int32(user_RoleToNumber(message.role));
    }
    if (message.username !== "") {
      writer.uint32(34).string(message.username);
    }
    if (message.email !== "") {
      writer.uint32(42).string(message.email);
    }
    if (message.nickname !== "") {
      writer.uint32(50).string(message.nickname);
    }
    if (message.avatarUrl !== "") {
      writer.uint32(58).string(message.avatarUrl);
    }
    if (message.description !== "") {
      writer.uint32(66).string(message.description);
    }
    if (message.password !== "") {
      writer.uint32(74).string(message.password);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(80).int32(stateToNumber(message.state));
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(90).fork()).join();
    }
    if (message.updateTime !== undefined) {
      Timestamp.encode(toTimestamp(message.updateTime), writer.uint32(98).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): User {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.role = user_RoleFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.avatarUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.updateTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<User>): User {
    return User.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<User>): User {
    const message = createBaseUser();
    message.name = object.name ?? "";
    message.role = object.role ?? User_Role.ROLE_UNSPECIFIED;
    message.username = object.username ?? "";
    message.email = object.email ?? "";
    message.nickname = object.nickname ?? "";
    message.avatarUrl = object.avatarUrl ?? "";
    message.description = object.description ?? "";
    message.password = object.password ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.createTime = object.createTime ?? undefined;
    message.updateTime = object.updateTime ?? undefined;
    return message;
  },
};

function createBaseListUsersRequest(): ListUsersRequest {
  return {};
}

export const ListUsersRequest: MessageFns<ListUsersRequest> = {
  encode(_: ListUsersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUsersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUsersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUsersRequest>): ListUsersRequest {
    return ListUsersRequest.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<ListUsersRequest>): ListUsersRequest {
    const message = createBaseListUsersRequest();
    return message;
  },
};

function createBaseListUsersResponse(): ListUsersResponse {
  return { users: [] };
}

export const ListUsersResponse: MessageFns<ListUsersResponse> = {
  encode(message: ListUsersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.users) {
      User.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUsersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUsersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.users.push(User.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUsersResponse>): ListUsersResponse {
    return ListUsersResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUsersResponse>): ListUsersResponse {
    const message = createBaseListUsersResponse();
    message.users = object.users?.map((e) => User.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetUserRequest(): GetUserRequest {
  return { name: "" };
}

export const GetUserRequest: MessageFns<GetUserRequest> = {
  encode(message: GetUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserRequest>): GetUserRequest {
    return GetUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserRequest>): GetUserRequest {
    const message = createBaseGetUserRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGetUserByUsernameRequest(): GetUserByUsernameRequest {
  return { username: "" };
}

export const GetUserByUsernameRequest: MessageFns<GetUserByUsernameRequest> = {
  encode(message: GetUserByUsernameRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserByUsernameRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserByUsernameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserByUsernameRequest>): GetUserByUsernameRequest {
    return GetUserByUsernameRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserByUsernameRequest>): GetUserByUsernameRequest {
    const message = createBaseGetUserByUsernameRequest();
    message.username = object.username ?? "";
    return message;
  },
};

function createBaseGetUserAvatarBinaryRequest(): GetUserAvatarBinaryRequest {
  return { name: "", httpBody: undefined };
}

export const GetUserAvatarBinaryRequest: MessageFns<GetUserAvatarBinaryRequest> = {
  encode(message: GetUserAvatarBinaryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.httpBody !== undefined) {
      HttpBody.encode(message.httpBody, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserAvatarBinaryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserAvatarBinaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.httpBody = HttpBody.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserAvatarBinaryRequest>): GetUserAvatarBinaryRequest {
    return GetUserAvatarBinaryRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserAvatarBinaryRequest>): GetUserAvatarBinaryRequest {
    const message = createBaseGetUserAvatarBinaryRequest();
    message.name = object.name ?? "";
    message.httpBody = (object.httpBody !== undefined && object.httpBody !== null)
      ? HttpBody.fromPartial(object.httpBody)
      : undefined;
    return message;
  },
};

function createBaseCreateUserRequest(): CreateUserRequest {
  return { user: undefined };
}

export const CreateUserRequest: MessageFns<CreateUserRequest> = {
  encode(message: CreateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateUserRequest>): CreateUserRequest {
    return CreateUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateUserRequest>): CreateUserRequest {
    const message = createBaseCreateUserRequest();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    return message;
  },
};

function createBaseUpdateUserRequest(): UpdateUserRequest {
  return { user: undefined, updateMask: undefined };
}

export const UpdateUserRequest: MessageFns<UpdateUserRequest> = {
  encode(message: UpdateUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.user !== undefined) {
      User.encode(message.user, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.user = User.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateUserRequest>): UpdateUserRequest {
    return UpdateUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateUserRequest>): UpdateUserRequest {
    const message = createBaseUpdateUserRequest();
    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteUserRequest(): DeleteUserRequest {
  return { name: "" };
}

export const DeleteUserRequest: MessageFns<DeleteUserRequest> = {
  encode(message: DeleteUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteUserRequest>): DeleteUserRequest {
    return DeleteUserRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteUserRequest>): DeleteUserRequest {
    const message = createBaseDeleteUserRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUserStats(): UserStats {
  return {
    name: "",
    memoDisplayTimestamps: [],
    memoTypeStats: undefined,
    tagCount: {},
    pinnedMemos: [],
    totalMemoCount: 0,
  };
}

export const UserStats: MessageFns<UserStats> = {
  encode(message: UserStats, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.memoDisplayTimestamps) {
      Timestamp.encode(toTimestamp(v!), writer.uint32(18).fork()).join();
    }
    if (message.memoTypeStats !== undefined) {
      UserStats_MemoTypeStats.encode(message.memoTypeStats, writer.uint32(26).fork()).join();
    }
    Object.entries(message.tagCount).forEach(([key, value]) => {
      UserStats_TagCountEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    for (const v of message.pinnedMemos) {
      writer.uint32(42).string(v!);
    }
    if (message.totalMemoCount !== 0) {
      writer.uint32(48).int32(message.totalMemoCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memoDisplayTimestamps.push(fromTimestamp(Timestamp.decode(reader, reader.uint32())));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.memoTypeStats = UserStats_MemoTypeStats.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = UserStats_TagCountEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.tagCount[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pinnedMemos.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.totalMemoCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats>): UserStats {
    return UserStats.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats>): UserStats {
    const message = createBaseUserStats();
    message.name = object.name ?? "";
    message.memoDisplayTimestamps = object.memoDisplayTimestamps?.map((e) => e) || [];
    message.memoTypeStats = (object.memoTypeStats !== undefined && object.memoTypeStats !== null)
      ? UserStats_MemoTypeStats.fromPartial(object.memoTypeStats)
      : undefined;
    message.tagCount = Object.entries(object.tagCount ?? {}).reduce<{ [key: string]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.pinnedMemos = object.pinnedMemos?.map((e) => e) || [];
    message.totalMemoCount = object.totalMemoCount ?? 0;
    return message;
  },
};

function createBaseUserStats_TagCountEntry(): UserStats_TagCountEntry {
  return { key: "", value: 0 };
}

export const UserStats_TagCountEntry: MessageFns<UserStats_TagCountEntry> = {
  encode(message: UserStats_TagCountEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats_TagCountEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats_TagCountEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats_TagCountEntry>): UserStats_TagCountEntry {
    return UserStats_TagCountEntry.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats_TagCountEntry>): UserStats_TagCountEntry {
    const message = createBaseUserStats_TagCountEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseUserStats_MemoTypeStats(): UserStats_MemoTypeStats {
  return { linkCount: 0, codeCount: 0, todoCount: 0, undoCount: 0 };
}

export const UserStats_MemoTypeStats: MessageFns<UserStats_MemoTypeStats> = {
  encode(message: UserStats_MemoTypeStats, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.linkCount !== 0) {
      writer.uint32(8).int32(message.linkCount);
    }
    if (message.codeCount !== 0) {
      writer.uint32(16).int32(message.codeCount);
    }
    if (message.todoCount !== 0) {
      writer.uint32(24).int32(message.todoCount);
    }
    if (message.undoCount !== 0) {
      writer.uint32(32).int32(message.undoCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStats_MemoTypeStats {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStats_MemoTypeStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.linkCount = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.codeCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.todoCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.undoCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserStats_MemoTypeStats>): UserStats_MemoTypeStats {
    return UserStats_MemoTypeStats.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserStats_MemoTypeStats>): UserStats_MemoTypeStats {
    const message = createBaseUserStats_MemoTypeStats();
    message.linkCount = object.linkCount ?? 0;
    message.codeCount = object.codeCount ?? 0;
    message.todoCount = object.todoCount ?? 0;
    message.undoCount = object.undoCount ?? 0;
    return message;
  },
};

function createBaseListAllUserStatsRequest(): ListAllUserStatsRequest {
  return {};
}

export const ListAllUserStatsRequest: MessageFns<ListAllUserStatsRequest> = {
  encode(_: ListAllUserStatsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAllUserStatsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAllUserStatsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAllUserStatsRequest>): ListAllUserStatsRequest {
    return ListAllUserStatsRequest.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<ListAllUserStatsRequest>): ListAllUserStatsRequest {
    const message = createBaseListAllUserStatsRequest();
    return message;
  },
};

function createBaseListAllUserStatsResponse(): ListAllUserStatsResponse {
  return { userStats: [] };
}

export const ListAllUserStatsResponse: MessageFns<ListAllUserStatsResponse> = {
  encode(message: ListAllUserStatsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.userStats) {
      UserStats.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListAllUserStatsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListAllUserStatsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userStats.push(UserStats.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListAllUserStatsResponse>): ListAllUserStatsResponse {
    return ListAllUserStatsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListAllUserStatsResponse>): ListAllUserStatsResponse {
    const message = createBaseListAllUserStatsResponse();
    message.userStats = object.userStats?.map((e) => UserStats.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetUserStatsRequest(): GetUserStatsRequest {
  return { name: "" };
}

export const GetUserStatsRequest: MessageFns<GetUserStatsRequest> = {
  encode(message: GetUserStatsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserStatsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserStatsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserStatsRequest>): GetUserStatsRequest {
    return GetUserStatsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserStatsRequest>): GetUserStatsRequest {
    const message = createBaseGetUserStatsRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUserSetting(): UserSetting {
  return { name: "", locale: "", appearance: "", memoVisibility: "" };
}

export const UserSetting: MessageFns<UserSetting> = {
  encode(message: UserSetting, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.locale !== "") {
      writer.uint32(18).string(message.locale);
    }
    if (message.appearance !== "") {
      writer.uint32(26).string(message.appearance);
    }
    if (message.memoVisibility !== "") {
      writer.uint32(34).string(message.memoVisibility);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSetting {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSetting();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.locale = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.appearance = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.memoVisibility = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserSetting>): UserSetting {
    return UserSetting.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserSetting>): UserSetting {
    const message = createBaseUserSetting();
    message.name = object.name ?? "";
    message.locale = object.locale ?? "";
    message.appearance = object.appearance ?? "";
    message.memoVisibility = object.memoVisibility ?? "";
    return message;
  },
};

function createBaseGetUserSettingRequest(): GetUserSettingRequest {
  return { name: "" };
}

export const GetUserSettingRequest: MessageFns<GetUserSettingRequest> = {
  encode(message: GetUserSettingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserSettingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserSettingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetUserSettingRequest>): GetUserSettingRequest {
    return GetUserSettingRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserSettingRequest>): GetUserSettingRequest {
    const message = createBaseGetUserSettingRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUpdateUserSettingRequest(): UpdateUserSettingRequest {
  return { setting: undefined, updateMask: undefined };
}

export const UpdateUserSettingRequest: MessageFns<UpdateUserSettingRequest> = {
  encode(message: UpdateUserSettingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.setting !== undefined) {
      UserSetting.encode(message.setting, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateUserSettingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserSettingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.setting = UserSetting.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateUserSettingRequest>): UpdateUserSettingRequest {
    return UpdateUserSettingRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateUserSettingRequest>): UpdateUserSettingRequest {
    const message = createBaseUpdateUserSettingRequest();
    message.setting = (object.setting !== undefined && object.setting !== null)
      ? UserSetting.fromPartial(object.setting)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseUserAccessToken(): UserAccessToken {
  return { accessToken: "", description: "", issuedAt: undefined, expiresAt: undefined };
}

export const UserAccessToken: MessageFns<UserAccessToken> = {
  encode(message: UserAccessToken, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accessToken !== "") {
      writer.uint32(10).string(message.accessToken);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.issuedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.issuedAt), writer.uint32(26).fork()).join();
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserAccessToken {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserAccessToken();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.issuedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UserAccessToken>): UserAccessToken {
    return UserAccessToken.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserAccessToken>): UserAccessToken {
    const message = createBaseUserAccessToken();
    message.accessToken = object.accessToken ?? "";
    message.description = object.description ?? "";
    message.issuedAt = object.issuedAt ?? undefined;
    message.expiresAt = object.expiresAt ?? undefined;
    return message;
  },
};

function createBaseListUserAccessTokensRequest(): ListUserAccessTokensRequest {
  return { name: "" };
}

export const ListUserAccessTokensRequest: MessageFns<ListUserAccessTokensRequest> = {
  encode(message: ListUserAccessTokensRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserAccessTokensRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserAccessTokensRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserAccessTokensRequest>): ListUserAccessTokensRequest {
    return ListUserAccessTokensRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserAccessTokensRequest>): ListUserAccessTokensRequest {
    const message = createBaseListUserAccessTokensRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListUserAccessTokensResponse(): ListUserAccessTokensResponse {
  return { accessTokens: [] };
}

export const ListUserAccessTokensResponse: MessageFns<ListUserAccessTokensResponse> = {
  encode(message: ListUserAccessTokensResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accessTokens) {
      UserAccessToken.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListUserAccessTokensResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListUserAccessTokensResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accessTokens.push(UserAccessToken.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListUserAccessTokensResponse>): ListUserAccessTokensResponse {
    return ListUserAccessTokensResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListUserAccessTokensResponse>): ListUserAccessTokensResponse {
    const message = createBaseListUserAccessTokensResponse();
    message.accessTokens = object.accessTokens?.map((e) => UserAccessToken.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreateUserAccessTokenRequest(): CreateUserAccessTokenRequest {
  return { name: "", description: "", expiresAt: undefined };
}

export const CreateUserAccessTokenRequest: MessageFns<CreateUserAccessTokenRequest> = {
  encode(message: CreateUserAccessTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.expiresAt !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAt), writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateUserAccessTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateUserAccessTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.expiresAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateUserAccessTokenRequest>): CreateUserAccessTokenRequest {
    return CreateUserAccessTokenRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateUserAccessTokenRequest>): CreateUserAccessTokenRequest {
    const message = createBaseCreateUserAccessTokenRequest();
    message.name = object.name ?? "";
    message.description = object.description ?? "";
    message.expiresAt = object.expiresAt ?? undefined;
    return message;
  },
};

function createBaseDeleteUserAccessTokenRequest(): DeleteUserAccessTokenRequest {
  return { name: "", accessToken: "" };
}

export const DeleteUserAccessTokenRequest: MessageFns<DeleteUserAccessTokenRequest> = {
  encode(message: DeleteUserAccessTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.accessToken !== "") {
      writer.uint32(18).string(message.accessToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserAccessTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserAccessTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteUserAccessTokenRequest>): DeleteUserAccessTokenRequest {
    return DeleteUserAccessTokenRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteUserAccessTokenRequest>): DeleteUserAccessTokenRequest {
    const message = createBaseDeleteUserAccessTokenRequest();
    message.name = object.name ?? "";
    message.accessToken = object.accessToken ?? "";
    return message;
  },
};

function createBaseShortcut(): Shortcut {
  return { id: "", title: "", filter: "" };
}

export const Shortcut: MessageFns<Shortcut> = {
  encode(message: Shortcut, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.filter !== "") {
      writer.uint32(26).string(message.filter);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Shortcut {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseShortcut();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Shortcut>): Shortcut {
    return Shortcut.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Shortcut>): Shortcut {
    const message = createBaseShortcut();
    message.id = object.id ?? "";
    message.title = object.title ?? "";
    message.filter = object.filter ?? "";
    return message;
  },
};

function createBaseListShortcutsRequest(): ListShortcutsRequest {
  return { parent: "" };
}

export const ListShortcutsRequest: MessageFns<ListShortcutsRequest> = {
  encode(message: ListShortcutsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListShortcutsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListShortcutsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListShortcutsRequest>): ListShortcutsRequest {
    return ListShortcutsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListShortcutsRequest>): ListShortcutsRequest {
    const message = createBaseListShortcutsRequest();
    message.parent = object.parent ?? "";
    return message;
  },
};

function createBaseListShortcutsResponse(): ListShortcutsResponse {
  return { shortcuts: [] };
}

export const ListShortcutsResponse: MessageFns<ListShortcutsResponse> = {
  encode(message: ListShortcutsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.shortcuts) {
      Shortcut.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListShortcutsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListShortcutsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.shortcuts.push(Shortcut.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListShortcutsResponse>): ListShortcutsResponse {
    return ListShortcutsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListShortcutsResponse>): ListShortcutsResponse {
    const message = createBaseListShortcutsResponse();
    message.shortcuts = object.shortcuts?.map((e) => Shortcut.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreateShortcutRequest(): CreateShortcutRequest {
  return { parent: "", shortcut: undefined, validateOnly: false };
}

export const CreateShortcutRequest: MessageFns<CreateShortcutRequest> = {
  encode(message: CreateShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.shortcut !== undefined) {
      Shortcut.encode(message.shortcut, writer.uint32(18).fork()).join();
    }
    if (message.validateOnly !== false) {
      writer.uint32(24).bool(message.validateOnly);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortcut = Shortcut.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.validateOnly = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateShortcutRequest>): CreateShortcutRequest {
    return CreateShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateShortcutRequest>): CreateShortcutRequest {
    const message = createBaseCreateShortcutRequest();
    message.parent = object.parent ?? "";
    message.shortcut = (object.shortcut !== undefined && object.shortcut !== null)
      ? Shortcut.fromPartial(object.shortcut)
      : undefined;
    message.validateOnly = object.validateOnly ?? false;
    return message;
  },
};

function createBaseUpdateShortcutRequest(): UpdateShortcutRequest {
  return { parent: "", shortcut: undefined, updateMask: undefined };
}

export const UpdateShortcutRequest: MessageFns<UpdateShortcutRequest> = {
  encode(message: UpdateShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.shortcut !== undefined) {
      Shortcut.encode(message.shortcut, writer.uint32(18).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortcut = Shortcut.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateShortcutRequest>): UpdateShortcutRequest {
    return UpdateShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateShortcutRequest>): UpdateShortcutRequest {
    const message = createBaseUpdateShortcutRequest();
    message.parent = object.parent ?? "";
    message.shortcut = (object.shortcut !== undefined && object.shortcut !== null)
      ? Shortcut.fromPartial(object.shortcut)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteShortcutRequest(): DeleteShortcutRequest {
  return { parent: "", id: "" };
}

export const DeleteShortcutRequest: MessageFns<DeleteShortcutRequest> = {
  encode(message: DeleteShortcutRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteShortcutRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteShortcutRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteShortcutRequest>): DeleteShortcutRequest {
    return DeleteShortcutRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteShortcutRequest>): DeleteShortcutRequest {
    const message = createBaseDeleteShortcutRequest();
    message.parent = object.parent ?? "";
    message.id = object.id ?? "";
    return message;
  },
};

export type UserServiceDefinition = typeof UserServiceDefinition;
export const UserServiceDefinition = {
  name: "UserService",
  fullName: "memos.api.v1.UserService",
  methods: {
    /** ListUsers returns a list of users. */
    listUsers: {
      name: "ListUsers",
      requestType: ListUsersRequest,
      requestStream: false,
      responseType: ListUsersResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [new Uint8Array([15, 18, 13, 47, 97, 112, 105, 47, 118, 49, 47, 117, 115, 101, 114, 115])],
        },
      },
    },
    /** GetUser gets a user by name. */
    getUser: {
      name: "GetUser",
      requestType: GetUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** GetUserByUsername gets a user by username. */
    getUserByUsername: {
      name: "GetUserByUsername",
      requestType: GetUserByUsernameRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([8, 117, 115, 101, 114, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
              58,
              117,
              115,
              101,
              114,
              110,
              97,
              109,
              101,
            ]),
          ],
        },
      },
    },
    /** GetUserAvatarBinary gets the avatar of a user. */
    getUserAvatarBinary: {
      name: "GetUserAvatarBinary",
      requestType: GetUserAvatarBinaryRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              102,
              105,
              108,
              101,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              118,
              97,
              116,
              97,
              114,
            ]),
          ],
        },
      },
    },
    /** CreateUser creates a new user. */
    createUser: {
      name: "CreateUser",
      requestType: CreateUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 117, 115, 101, 114])],
          578365826: [
            new Uint8Array([
              21,
              58,
              4,
              117,
              115,
              101,
              114,
              34,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
            ]),
          ],
        },
      },
    },
    /** UpdateUser updates a user. */
    updateUser: {
      name: "UpdateUser",
      requestType: UpdateUserRequest,
      requestStream: false,
      responseType: User,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([16, 117, 115, 101, 114, 44, 117, 112, 100, 97, 116, 101, 95, 109, 97, 115, 107])],
          578365826: [
            new Uint8Array([
              35,
              58,
              4,
              117,
              115,
              101,
              114,
              50,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              117,
              115,
              101,
              114,
              46,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteUser deletes a user. */
    deleteUser: {
      name: "DeleteUser",
      requestType: DeleteUserRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              42,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** ListAllUserStats returns all user stats. */
    listAllUserStats: {
      name: "ListAllUserStats",
      requestType: ListAllUserStatsRequest,
      requestStream: false,
      responseType: ListAllUserStatsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              23,
              34,
              21,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              117,
              115,
              101,
              114,
              115,
              47,
              45,
              47,
              115,
              116,
              97,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetUserStats returns the stats of a user. */
    getUserStats: {
      name: "GetUserStats",
      requestType: GetUserStatsRequest,
      requestStream: false,
      responseType: UserStats,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              30,
              18,
              28,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              116,
              97,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** GetUserSetting gets the setting of a user. */
    getUserSetting: {
      name: "GetUserSetting",
      requestType: GetUserSettingRequest,
      requestStream: false,
      responseType: UserSetting,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
            ]),
          ],
        },
      },
    },
    /** UpdateUserSetting updates the setting of a user. */
    updateUserSetting: {
      name: "UpdateUserSetting",
      requestType: UpdateUserSettingRequest,
      requestStream: false,
      responseType: UserSetting,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              19,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              49,
              58,
              7,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              50,
              38,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              46,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              47,
              115,
              101,
              116,
              116,
              105,
              110,
              103,
              125,
            ]),
          ],
        },
      },
    },
    /** ListUserAccessTokens returns a list of access tokens for a user. */
    listUserAccessTokens: {
      name: "ListUserAccessTokens",
      requestType: ListUserAccessTokensRequest,
      requestStream: false,
      responseType: ListUserAccessTokensResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              38,
              18,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** CreateUserAccessToken creates a new access token for a user. */
    createUserAccessToken: {
      name: "CreateUserAccessToken",
      requestType: CreateUserAccessTokenRequest,
      requestStream: false,
      responseType: UserAccessToken,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              41,
              58,
              1,
              42,
              34,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** DeleteUserAccessToken deletes an access token for a user. */
    deleteUserAccessToken: {
      name: "DeleteUserAccessToken",
      requestType: DeleteUserAccessTokenRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([17, 110, 97, 109, 101, 44, 97, 99, 99, 101, 115, 115, 95, 116, 111, 107, 101, 110])],
          578365826: [
            new Uint8Array([
              53,
              42,
              51,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
              115,
              47,
              123,
              97,
              99,
              99,
              101,
              115,
              115,
              95,
              116,
              111,
              107,
              101,
              110,
              125,
            ]),
          ],
        },
      },
    },
    /** ListShortcuts returns a list of shortcuts for a user. */
    listShortcuts: {
      name: "ListShortcuts",
      requestType: ListShortcutsRequest,
      requestStream: false,
      responseType: ListShortcutsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([6, 112, 97, 114, 101, 110, 116])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** CreateShortcut creates a new shortcut for a user. */
    createShortcut: {
      name: "CreateShortcut",
      requestType: CreateShortcutRequest,
      requestStream: false,
      responseType: Shortcut,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([15, 112, 97, 114, 101, 110, 116, 44, 115, 104, 111, 114, 116, 99, 117, 116])],
          578365826: [
            new Uint8Array([
              46,
              58,
              8,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              34,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** UpdateShortcut updates a shortcut for a user. */
    updateShortcut: {
      name: "UpdateShortcut",
      requestType: UpdateShortcutRequest,
      requestStream: false,
      responseType: Shortcut,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              27,
              112,
              97,
              114,
              101,
              110,
              116,
              44,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              60,
              58,
              8,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              50,
              48,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
              47,
              123,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              46,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteShortcut deletes a shortcut for a user. */
    deleteShortcut: {
      name: "DeleteShortcut",
      requestType: DeleteShortcutRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([9, 112, 97, 114, 101, 110, 116, 44, 105, 100])],
          578365826: [
            new Uint8Array([
              41,
              42,
              39,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              115,
              104,
              111,
              114,
              116,
              99,
              117,
              116,
              115,
              47,
              123,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
