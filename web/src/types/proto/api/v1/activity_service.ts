// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/activity_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "../../google/protobuf/timestamp";

export const protobufPackage = "memos.api.v1";

export interface Activity {
  /**
   * The name of the activity.
   * Format: activities/{id}
   */
  name: string;
  /**
   * The name of the creator.
   * Format: users/{user}
   */
  creator: string;
  /** The type of the activity. */
  type: string;
  /** The level of the activity. */
  level: string;
  /** The create time of the activity. */
  createTime?:
    | Date
    | undefined;
  /** The payload of the activity. */
  payload?: ActivityPayload | undefined;
}

export interface ActivityPayload {
  memoComment?: ActivityMemoCommentPayload | undefined;
}

/** ActivityMemoCommentPayload represents the payload of a memo comment activity. */
export interface ActivityMemoCommentPayload {
  /**
   * The memo name of comment.
   * Refer to `Memo.name`.
   */
  memo: string;
  /** The name of related memo. */
  relatedMemo: string;
}

export interface GetActivityRequest {
  /**
   * The name of the activity.
   * Format: activities/{id}, id is the system generated auto-incremented id.
   */
  name: string;
}

function createBaseActivity(): Activity {
  return { name: "", creator: "", type: "", level: "", createTime: undefined, payload: undefined };
}

export const Activity: MessageFns<Activity> = {
  encode(message: Activity, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.creator !== "") {
      writer.uint32(18).string(message.creator);
    }
    if (message.type !== "") {
      writer.uint32(26).string(message.type);
    }
    if (message.level !== "") {
      writer.uint32(34).string(message.level);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(42).fork()).join();
    }
    if (message.payload !== undefined) {
      ActivityPayload.encode(message.payload, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Activity {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.level = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.payload = ActivityPayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Activity>): Activity {
    return Activity.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Activity>): Activity {
    const message = createBaseActivity();
    message.name = object.name ?? "";
    message.creator = object.creator ?? "";
    message.type = object.type ?? "";
    message.level = object.level ?? "";
    message.createTime = object.createTime ?? undefined;
    message.payload = (object.payload !== undefined && object.payload !== null)
      ? ActivityPayload.fromPartial(object.payload)
      : undefined;
    return message;
  },
};

function createBaseActivityPayload(): ActivityPayload {
  return { memoComment: undefined };
}

export const ActivityPayload: MessageFns<ActivityPayload> = {
  encode(message: ActivityPayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memoComment !== undefined) {
      ActivityMemoCommentPayload.encode(message.memoComment, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityPayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memoComment = ActivityMemoCommentPayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ActivityPayload>): ActivityPayload {
    return ActivityPayload.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ActivityPayload>): ActivityPayload {
    const message = createBaseActivityPayload();
    message.memoComment = (object.memoComment !== undefined && object.memoComment !== null)
      ? ActivityMemoCommentPayload.fromPartial(object.memoComment)
      : undefined;
    return message;
  },
};

function createBaseActivityMemoCommentPayload(): ActivityMemoCommentPayload {
  return { memo: "", relatedMemo: "" };
}

export const ActivityMemoCommentPayload: MessageFns<ActivityMemoCommentPayload> = {
  encode(message: ActivityMemoCommentPayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== "") {
      writer.uint32(10).string(message.memo);
    }
    if (message.relatedMemo !== "") {
      writer.uint32(18).string(message.relatedMemo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityMemoCommentPayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityMemoCommentPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relatedMemo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ActivityMemoCommentPayload>): ActivityMemoCommentPayload {
    return ActivityMemoCommentPayload.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ActivityMemoCommentPayload>): ActivityMemoCommentPayload {
    const message = createBaseActivityMemoCommentPayload();
    message.memo = object.memo ?? "";
    message.relatedMemo = object.relatedMemo ?? "";
    return message;
  },
};

function createBaseGetActivityRequest(): GetActivityRequest {
  return { name: "" };
}

export const GetActivityRequest: MessageFns<GetActivityRequest> = {
  encode(message: GetActivityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetActivityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetActivityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetActivityRequest>): GetActivityRequest {
    return GetActivityRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetActivityRequest>): GetActivityRequest {
    const message = createBaseGetActivityRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type ActivityServiceDefinition = typeof ActivityServiceDefinition;
export const ActivityServiceDefinition = {
  name: "ActivityService",
  fullName: "memos.api.v1.ActivityService",
  methods: {
    /** GetActivity returns the activity with the given id. */
    getActivity: {
      name: "GetActivity",
      requestType: GetActivityRequest,
      requestStream: false,
      responseType: Activity,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              97,
              99,
              116,
              105,
              118,
              105,
              116,
              105,
              101,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
