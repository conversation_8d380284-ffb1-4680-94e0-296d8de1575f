// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/memo_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";
import { Direction, directionFromJSON, directionToNumber, State, stateFromJSON, stateToNumber } from "./common";
import { Node } from "./markdown_service";
import { Reaction } from "./reaction_service";
import { Resource } from "./resource_service";

export const protobufPackage = "memos.api.v1";

export enum Visibility {
  VISIBILITY_UNSPECIFIED = "VISIBILITY_UNSPECIFIED",
  PRIVATE = "PRIVATE",
  PROTECTED = "PROTECTED",
  PUBLIC = "PUBLIC",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function visibilityFromJSON(object: any): Visibility {
  switch (object) {
    case 0:
    case "VISIBILITY_UNSPECIFIED":
      return Visibility.VISIBILITY_UNSPECIFIED;
    case 1:
    case "PRIVATE":
      return Visibility.PRIVATE;
    case 2:
    case "PROTECTED":
      return Visibility.PROTECTED;
    case 3:
    case "PUBLIC":
      return Visibility.PUBLIC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Visibility.UNRECOGNIZED;
  }
}

export function visibilityToNumber(object: Visibility): number {
  switch (object) {
    case Visibility.VISIBILITY_UNSPECIFIED:
      return 0;
    case Visibility.PRIVATE:
      return 1;
    case Visibility.PROTECTED:
      return 2;
    case Visibility.PUBLIC:
      return 3;
    case Visibility.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface Memo {
  /**
   * The name of the memo.
   * Format: memos/{memo}, memo is the user defined id or uuid.
   */
  name: string;
  state: State;
  /**
   * The name of the creator.
   * Format: users/{user}
   */
  creator: string;
  createTime?: Date | undefined;
  updateTime?: Date | undefined;
  displayTime?: Date | undefined;
  content: string;
  nodes: Node[];
  visibility: Visibility;
  tags: string[];
  pinned: boolean;
  resources: Resource[];
  relations: MemoRelation[];
  reactions: Reaction[];
  property?:
    | Memo_Property
    | undefined;
  /**
   * The name of the parent memo.
   * Format: memos/{id}
   */
  parent?:
    | string
    | undefined;
  /** The snippet of the memo content. Plain text only. */
  snippet: string;
  /** The location of the memo. */
  location?: Location | undefined;
}

export interface Memo_Property {
  hasLink: boolean;
  hasTaskList: boolean;
  hasCode: boolean;
  hasIncompleteTasks: boolean;
}

export interface Location {
  placeholder: string;
  latitude: number;
  longitude: number;
}

export interface CreateMemoRequest {
  /** The memo to create. */
  memo?: Memo | undefined;
}

export interface ListMemosRequest {
  /**
   * The parent is the owner of the memos.
   * If not specified or `users/-`, it will list all memos.
   */
  parent: string;
  /** The maximum number of memos to return. */
  pageSize: number;
  /**
   * A page token, received from a previous `ListMemos` call.
   * Provide this to retrieve the subsequent page.
   */
  pageToken: string;
  /**
   * The state of the memos to list.
   * Default to `NORMAL`. Set to `ARCHIVED` to list archived memos.
   */
  state: State;
  /**
   * What field to sort the results by.
   * Default to display_time.
   */
  sort: string;
  /**
   * The direction to sort the results by.
   * Default to DESC.
   */
  direction: Direction;
  /**
   * Filter is a CEL expression to filter memos.
   * Refer to `Shortcut.filter`.
   */
  filter: string;
  /**
   * [Deprecated] Old filter contains some specific conditions to filter memos.
   * Format: "creator == 'users/{user}' && visibilities == ['PUBLIC', 'PROTECTED']"
   */
  oldFilter: string;
}

export interface ListMemosResponse {
  memos: Memo[];
  /**
   * A token, which can be sent as `page_token` to retrieve the next page.
   * If this field is omitted, there are no subsequent pages.
   */
  nextPageToken: string;
}

export interface GetMemoRequest {
  /** The name of the memo. */
  name: string;
}

export interface UpdateMemoRequest {
  /**
   * The memo to update.
   * The `name` field is required.
   */
  memo?: Memo | undefined;
  updateMask?: string[] | undefined;
}

export interface DeleteMemoRequest {
  /** The name of the memo. */
  name: string;
}

export interface RenameMemoTagRequest {
  /**
   * The parent, who owns the tags.
   * Format: memos/{id}. Use "memos/-" to rename all tags.
   */
  parent: string;
  oldTag: string;
  newTag: string;
}

export interface DeleteMemoTagRequest {
  /**
   * The parent, who owns the tags.
   * Format: memos/{id}. Use "memos/-" to delete all tags.
   */
  parent: string;
  tag: string;
  deleteRelatedMemos: boolean;
}

export interface SetMemoResourcesRequest {
  /** The name of the memo. */
  name: string;
  resources: Resource[];
}

export interface ListMemoResourcesRequest {
  /** The name of the memo. */
  name: string;
}

export interface ListMemoResourcesResponse {
  resources: Resource[];
}

export interface MemoRelation {
  memo?: MemoRelation_Memo | undefined;
  relatedMemo?: MemoRelation_Memo | undefined;
  type: MemoRelation_Type;
}

export enum MemoRelation_Type {
  TYPE_UNSPECIFIED = "TYPE_UNSPECIFIED",
  REFERENCE = "REFERENCE",
  COMMENT = "COMMENT",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function memoRelation_TypeFromJSON(object: any): MemoRelation_Type {
  switch (object) {
    case 0:
    case "TYPE_UNSPECIFIED":
      return MemoRelation_Type.TYPE_UNSPECIFIED;
    case 1:
    case "REFERENCE":
      return MemoRelation_Type.REFERENCE;
    case 2:
    case "COMMENT":
      return MemoRelation_Type.COMMENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MemoRelation_Type.UNRECOGNIZED;
  }
}

export function memoRelation_TypeToNumber(object: MemoRelation_Type): number {
  switch (object) {
    case MemoRelation_Type.TYPE_UNSPECIFIED:
      return 0;
    case MemoRelation_Type.REFERENCE:
      return 1;
    case MemoRelation_Type.COMMENT:
      return 2;
    case MemoRelation_Type.UNRECOGNIZED:
    default:
      return -1;
  }
}

export interface MemoRelation_Memo {
  /**
   * The name of the memo.
   * Format: memos/{id}
   */
  name: string;
  uid: string;
  /** The snippet of the memo content. Plain text only. */
  snippet: string;
}

export interface SetMemoRelationsRequest {
  /** The name of the memo. */
  name: string;
  relations: MemoRelation[];
}

export interface ListMemoRelationsRequest {
  /** The name of the memo. */
  name: string;
}

export interface ListMemoRelationsResponse {
  relations: MemoRelation[];
}

export interface CreateMemoCommentRequest {
  /** The name of the memo. */
  name: string;
  /** The comment to create. */
  comment?: Memo | undefined;
}

export interface ListMemoCommentsRequest {
  /** The name of the memo. */
  name: string;
}

export interface ListMemoCommentsResponse {
  memos: Memo[];
}

export interface ListMemoReactionsRequest {
  /** The name of the memo. */
  name: string;
}

export interface ListMemoReactionsResponse {
  reactions: Reaction[];
}

export interface UpsertMemoReactionRequest {
  /** The name of the memo. */
  name: string;
  reaction?: Reaction | undefined;
}

export interface DeleteMemoReactionRequest {
  /**
   * The id of the reaction.
   * Refer to the `Reaction.id`.
   */
  id: number;
}

function createBaseMemo(): Memo {
  return {
    name: "",
    state: State.STATE_UNSPECIFIED,
    creator: "",
    createTime: undefined,
    updateTime: undefined,
    displayTime: undefined,
    content: "",
    nodes: [],
    visibility: Visibility.VISIBILITY_UNSPECIFIED,
    tags: [],
    pinned: false,
    resources: [],
    relations: [],
    reactions: [],
    property: undefined,
    parent: undefined,
    snippet: "",
    location: undefined,
  };
}

export const Memo: MessageFns<Memo> = {
  encode(message: Memo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(24).int32(stateToNumber(message.state));
    }
    if (message.creator !== "") {
      writer.uint32(34).string(message.creator);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(42).fork()).join();
    }
    if (message.updateTime !== undefined) {
      Timestamp.encode(toTimestamp(message.updateTime), writer.uint32(50).fork()).join();
    }
    if (message.displayTime !== undefined) {
      Timestamp.encode(toTimestamp(message.displayTime), writer.uint32(58).fork()).join();
    }
    if (message.content !== "") {
      writer.uint32(66).string(message.content);
    }
    for (const v of message.nodes) {
      Node.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.visibility !== Visibility.VISIBILITY_UNSPECIFIED) {
      writer.uint32(80).int32(visibilityToNumber(message.visibility));
    }
    for (const v of message.tags) {
      writer.uint32(90).string(v!);
    }
    if (message.pinned !== false) {
      writer.uint32(96).bool(message.pinned);
    }
    for (const v of message.resources) {
      Resource.encode(v!, writer.uint32(114).fork()).join();
    }
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(122).fork()).join();
    }
    for (const v of message.reactions) {
      Reaction.encode(v!, writer.uint32(130).fork()).join();
    }
    if (message.property !== undefined) {
      Memo_Property.encode(message.property, writer.uint32(138).fork()).join();
    }
    if (message.parent !== undefined) {
      writer.uint32(146).string(message.parent);
    }
    if (message.snippet !== "") {
      writer.uint32(154).string(message.snippet);
    }
    if (message.location !== undefined) {
      Location.encode(message.location, writer.uint32(162).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Memo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.updateTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.displayTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.nodes.push(Node.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.visibility = visibilityFromJSON(reader.int32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.pinned = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.resources.push(Resource.decode(reader, reader.uint32()));
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.reactions.push(Reaction.decode(reader, reader.uint32()));
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.property = Memo_Property.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.snippet = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.location = Location.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Memo>): Memo {
    return Memo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Memo>): Memo {
    const message = createBaseMemo();
    message.name = object.name ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.creator = object.creator ?? "";
    message.createTime = object.createTime ?? undefined;
    message.updateTime = object.updateTime ?? undefined;
    message.displayTime = object.displayTime ?? undefined;
    message.content = object.content ?? "";
    message.nodes = object.nodes?.map((e) => Node.fromPartial(e)) || [];
    message.visibility = object.visibility ?? Visibility.VISIBILITY_UNSPECIFIED;
    message.tags = object.tags?.map((e) => e) || [];
    message.pinned = object.pinned ?? false;
    message.resources = object.resources?.map((e) => Resource.fromPartial(e)) || [];
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    message.reactions = object.reactions?.map((e) => Reaction.fromPartial(e)) || [];
    message.property = (object.property !== undefined && object.property !== null)
      ? Memo_Property.fromPartial(object.property)
      : undefined;
    message.parent = object.parent ?? undefined;
    message.snippet = object.snippet ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? Location.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseMemo_Property(): Memo_Property {
  return { hasLink: false, hasTaskList: false, hasCode: false, hasIncompleteTasks: false };
}

export const Memo_Property: MessageFns<Memo_Property> = {
  encode(message: Memo_Property, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasLink !== false) {
      writer.uint32(8).bool(message.hasLink);
    }
    if (message.hasTaskList !== false) {
      writer.uint32(16).bool(message.hasTaskList);
    }
    if (message.hasCode !== false) {
      writer.uint32(24).bool(message.hasCode);
    }
    if (message.hasIncompleteTasks !== false) {
      writer.uint32(32).bool(message.hasIncompleteTasks);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Memo_Property {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemo_Property();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasLink = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasTaskList = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.hasCode = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.hasIncompleteTasks = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Memo_Property>): Memo_Property {
    return Memo_Property.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Memo_Property>): Memo_Property {
    const message = createBaseMemo_Property();
    message.hasLink = object.hasLink ?? false;
    message.hasTaskList = object.hasTaskList ?? false;
    message.hasCode = object.hasCode ?? false;
    message.hasIncompleteTasks = object.hasIncompleteTasks ?? false;
    return message;
  },
};

function createBaseLocation(): Location {
  return { placeholder: "", latitude: 0, longitude: 0 };
}

export const Location: MessageFns<Location> = {
  encode(message: Location, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.placeholder !== "") {
      writer.uint32(10).string(message.placeholder);
    }
    if (message.latitude !== 0) {
      writer.uint32(17).double(message.latitude);
    }
    if (message.longitude !== 0) {
      writer.uint32(25).double(message.longitude);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Location {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.placeholder = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.latitude = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.longitude = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Location>): Location {
    return Location.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Location>): Location {
    const message = createBaseLocation();
    message.placeholder = object.placeholder ?? "";
    message.latitude = object.latitude ?? 0;
    message.longitude = object.longitude ?? 0;
    return message;
  },
};

function createBaseCreateMemoRequest(): CreateMemoRequest {
  return { memo: undefined };
}

export const CreateMemoRequest: MessageFns<CreateMemoRequest> = {
  encode(message: CreateMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = Memo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateMemoRequest>): CreateMemoRequest {
    return CreateMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateMemoRequest>): CreateMemoRequest {
    const message = createBaseCreateMemoRequest();
    message.memo = (object.memo !== undefined && object.memo !== null) ? Memo.fromPartial(object.memo) : undefined;
    return message;
  },
};

function createBaseListMemosRequest(): ListMemosRequest {
  return {
    parent: "",
    pageSize: 0,
    pageToken: "",
    state: State.STATE_UNSPECIFIED,
    sort: "",
    direction: Direction.DIRECTION_UNSPECIFIED,
    filter: "",
    oldFilter: "",
  };
}

export const ListMemosRequest: MessageFns<ListMemosRequest> = {
  encode(message: ListMemosRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.pageSize !== 0) {
      writer.uint32(16).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(26).string(message.pageToken);
    }
    if (message.state !== State.STATE_UNSPECIFIED) {
      writer.uint32(32).int32(stateToNumber(message.state));
    }
    if (message.sort !== "") {
      writer.uint32(42).string(message.sort);
    }
    if (message.direction !== Direction.DIRECTION_UNSPECIFIED) {
      writer.uint32(48).int32(directionToNumber(message.direction));
    }
    if (message.filter !== "") {
      writer.uint32(58).string(message.filter);
    }
    if (message.oldFilter !== "") {
      writer.uint32(66).string(message.oldFilter);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemosRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemosRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.state = stateFromJSON(reader.int32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.sort = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.direction = directionFromJSON(reader.int32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.filter = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.oldFilter = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemosRequest>): ListMemosRequest {
    return ListMemosRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemosRequest>): ListMemosRequest {
    const message = createBaseListMemosRequest();
    message.parent = object.parent ?? "";
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.state = object.state ?? State.STATE_UNSPECIFIED;
    message.sort = object.sort ?? "";
    message.direction = object.direction ?? Direction.DIRECTION_UNSPECIFIED;
    message.filter = object.filter ?? "";
    message.oldFilter = object.oldFilter ?? "";
    return message;
  },
};

function createBaseListMemosResponse(): ListMemosResponse {
  return { memos: [], nextPageToken: "" };
}

export const ListMemosResponse: MessageFns<ListMemosResponse> = {
  encode(message: ListMemosResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.memos) {
      Memo.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextPageToken !== "") {
      writer.uint32(18).string(message.nextPageToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemosResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemosResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memos.push(Memo.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemosResponse>): ListMemosResponse {
    return ListMemosResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemosResponse>): ListMemosResponse {
    const message = createBaseListMemosResponse();
    message.memos = object.memos?.map((e) => Memo.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? "";
    return message;
  },
};

function createBaseGetMemoRequest(): GetMemoRequest {
  return { name: "" };
}

export const GetMemoRequest: MessageFns<GetMemoRequest> = {
  encode(message: GetMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetMemoRequest>): GetMemoRequest {
    return GetMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetMemoRequest>): GetMemoRequest {
    const message = createBaseGetMemoRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUpdateMemoRequest(): UpdateMemoRequest {
  return { memo: undefined, updateMask: undefined };
}

export const UpdateMemoRequest: MessageFns<UpdateMemoRequest> = {
  encode(message: UpdateMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = Memo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateMemoRequest>): UpdateMemoRequest {
    return UpdateMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateMemoRequest>): UpdateMemoRequest {
    const message = createBaseUpdateMemoRequest();
    message.memo = (object.memo !== undefined && object.memo !== null) ? Memo.fromPartial(object.memo) : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteMemoRequest(): DeleteMemoRequest {
  return { name: "" };
}

export const DeleteMemoRequest: MessageFns<DeleteMemoRequest> = {
  encode(message: DeleteMemoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoRequest>): DeleteMemoRequest {
    return DeleteMemoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoRequest>): DeleteMemoRequest {
    const message = createBaseDeleteMemoRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseRenameMemoTagRequest(): RenameMemoTagRequest {
  return { parent: "", oldTag: "", newTag: "" };
}

export const RenameMemoTagRequest: MessageFns<RenameMemoTagRequest> = {
  encode(message: RenameMemoTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.oldTag !== "") {
      writer.uint32(18).string(message.oldTag);
    }
    if (message.newTag !== "") {
      writer.uint32(26).string(message.newTag);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameMemoTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameMemoTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.oldTag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.newTag = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<RenameMemoTagRequest>): RenameMemoTagRequest {
    return RenameMemoTagRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<RenameMemoTagRequest>): RenameMemoTagRequest {
    const message = createBaseRenameMemoTagRequest();
    message.parent = object.parent ?? "";
    message.oldTag = object.oldTag ?? "";
    message.newTag = object.newTag ?? "";
    return message;
  },
};

function createBaseDeleteMemoTagRequest(): DeleteMemoTagRequest {
  return { parent: "", tag: "", deleteRelatedMemos: false };
}

export const DeleteMemoTagRequest: MessageFns<DeleteMemoTagRequest> = {
  encode(message: DeleteMemoTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parent !== "") {
      writer.uint32(10).string(message.parent);
    }
    if (message.tag !== "") {
      writer.uint32(18).string(message.tag);
    }
    if (message.deleteRelatedMemos !== false) {
      writer.uint32(24).bool(message.deleteRelatedMemos);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parent = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.deleteRelatedMemos = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoTagRequest>): DeleteMemoTagRequest {
    return DeleteMemoTagRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoTagRequest>): DeleteMemoTagRequest {
    const message = createBaseDeleteMemoTagRequest();
    message.parent = object.parent ?? "";
    message.tag = object.tag ?? "";
    message.deleteRelatedMemos = object.deleteRelatedMemos ?? false;
    return message;
  },
};

function createBaseSetMemoResourcesRequest(): SetMemoResourcesRequest {
  return { name: "", resources: [] };
}

export const SetMemoResourcesRequest: MessageFns<SetMemoResourcesRequest> = {
  encode(message: SetMemoResourcesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.resources) {
      Resource.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetMemoResourcesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetMemoResourcesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.resources.push(Resource.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SetMemoResourcesRequest>): SetMemoResourcesRequest {
    return SetMemoResourcesRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SetMemoResourcesRequest>): SetMemoResourcesRequest {
    const message = createBaseSetMemoResourcesRequest();
    message.name = object.name ?? "";
    message.resources = object.resources?.map((e) => Resource.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListMemoResourcesRequest(): ListMemoResourcesRequest {
  return { name: "" };
}

export const ListMemoResourcesRequest: MessageFns<ListMemoResourcesRequest> = {
  encode(message: ListMemoResourcesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoResourcesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoResourcesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoResourcesRequest>): ListMemoResourcesRequest {
    return ListMemoResourcesRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoResourcesRequest>): ListMemoResourcesRequest {
    const message = createBaseListMemoResourcesRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListMemoResourcesResponse(): ListMemoResourcesResponse {
  return { resources: [] };
}

export const ListMemoResourcesResponse: MessageFns<ListMemoResourcesResponse> = {
  encode(message: ListMemoResourcesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.resources) {
      Resource.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoResourcesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoResourcesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resources.push(Resource.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoResourcesResponse>): ListMemoResourcesResponse {
    return ListMemoResourcesResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoResourcesResponse>): ListMemoResourcesResponse {
    const message = createBaseListMemoResourcesResponse();
    message.resources = object.resources?.map((e) => Resource.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMemoRelation(): MemoRelation {
  return { memo: undefined, relatedMemo: undefined, type: MemoRelation_Type.TYPE_UNSPECIFIED };
}

export const MemoRelation: MessageFns<MemoRelation> = {
  encode(message: MemoRelation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memo !== undefined) {
      MemoRelation_Memo.encode(message.memo, writer.uint32(10).fork()).join();
    }
    if (message.relatedMemo !== undefined) {
      MemoRelation_Memo.encode(message.relatedMemo, writer.uint32(18).fork()).join();
    }
    if (message.type !== MemoRelation_Type.TYPE_UNSPECIFIED) {
      writer.uint32(24).int32(memoRelation_TypeToNumber(message.type));
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MemoRelation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemoRelation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memo = MemoRelation_Memo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relatedMemo = MemoRelation_Memo.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = memoRelation_TypeFromJSON(reader.int32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MemoRelation>): MemoRelation {
    return MemoRelation.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MemoRelation>): MemoRelation {
    const message = createBaseMemoRelation();
    message.memo = (object.memo !== undefined && object.memo !== null)
      ? MemoRelation_Memo.fromPartial(object.memo)
      : undefined;
    message.relatedMemo = (object.relatedMemo !== undefined && object.relatedMemo !== null)
      ? MemoRelation_Memo.fromPartial(object.relatedMemo)
      : undefined;
    message.type = object.type ?? MemoRelation_Type.TYPE_UNSPECIFIED;
    return message;
  },
};

function createBaseMemoRelation_Memo(): MemoRelation_Memo {
  return { name: "", uid: "", snippet: "" };
}

export const MemoRelation_Memo: MessageFns<MemoRelation_Memo> = {
  encode(message: MemoRelation_Memo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.uid !== "") {
      writer.uint32(18).string(message.uid);
    }
    if (message.snippet !== "") {
      writer.uint32(26).string(message.snippet);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MemoRelation_Memo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMemoRelation_Memo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.uid = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.snippet = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<MemoRelation_Memo>): MemoRelation_Memo {
    return MemoRelation_Memo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<MemoRelation_Memo>): MemoRelation_Memo {
    const message = createBaseMemoRelation_Memo();
    message.name = object.name ?? "";
    message.uid = object.uid ?? "";
    message.snippet = object.snippet ?? "";
    return message;
  },
};

function createBaseSetMemoRelationsRequest(): SetMemoRelationsRequest {
  return { name: "", relations: [] };
}

export const SetMemoRelationsRequest: MessageFns<SetMemoRelationsRequest> = {
  encode(message: SetMemoRelationsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetMemoRelationsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetMemoRelationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<SetMemoRelationsRequest>): SetMemoRelationsRequest {
    return SetMemoRelationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SetMemoRelationsRequest>): SetMemoRelationsRequest {
    const message = createBaseSetMemoRelationsRequest();
    message.name = object.name ?? "";
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListMemoRelationsRequest(): ListMemoRelationsRequest {
  return { name: "" };
}

export const ListMemoRelationsRequest: MessageFns<ListMemoRelationsRequest> = {
  encode(message: ListMemoRelationsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoRelationsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoRelationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoRelationsRequest>): ListMemoRelationsRequest {
    return ListMemoRelationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoRelationsRequest>): ListMemoRelationsRequest {
    const message = createBaseListMemoRelationsRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListMemoRelationsResponse(): ListMemoRelationsResponse {
  return { relations: [] };
}

export const ListMemoRelationsResponse: MessageFns<ListMemoRelationsResponse> = {
  encode(message: ListMemoRelationsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.relations) {
      MemoRelation.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoRelationsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoRelationsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.relations.push(MemoRelation.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoRelationsResponse>): ListMemoRelationsResponse {
    return ListMemoRelationsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoRelationsResponse>): ListMemoRelationsResponse {
    const message = createBaseListMemoRelationsResponse();
    message.relations = object.relations?.map((e) => MemoRelation.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreateMemoCommentRequest(): CreateMemoCommentRequest {
  return { name: "", comment: undefined };
}

export const CreateMemoCommentRequest: MessageFns<CreateMemoCommentRequest> = {
  encode(message: CreateMemoCommentRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.comment !== undefined) {
      Memo.encode(message.comment, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMemoCommentRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMemoCommentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.comment = Memo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateMemoCommentRequest>): CreateMemoCommentRequest {
    return CreateMemoCommentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateMemoCommentRequest>): CreateMemoCommentRequest {
    const message = createBaseCreateMemoCommentRequest();
    message.name = object.name ?? "";
    message.comment = (object.comment !== undefined && object.comment !== null)
      ? Memo.fromPartial(object.comment)
      : undefined;
    return message;
  },
};

function createBaseListMemoCommentsRequest(): ListMemoCommentsRequest {
  return { name: "" };
}

export const ListMemoCommentsRequest: MessageFns<ListMemoCommentsRequest> = {
  encode(message: ListMemoCommentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoCommentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoCommentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoCommentsRequest>): ListMemoCommentsRequest {
    return ListMemoCommentsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoCommentsRequest>): ListMemoCommentsRequest {
    const message = createBaseListMemoCommentsRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListMemoCommentsResponse(): ListMemoCommentsResponse {
  return { memos: [] };
}

export const ListMemoCommentsResponse: MessageFns<ListMemoCommentsResponse> = {
  encode(message: ListMemoCommentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.memos) {
      Memo.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoCommentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoCommentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memos.push(Memo.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoCommentsResponse>): ListMemoCommentsResponse {
    return ListMemoCommentsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoCommentsResponse>): ListMemoCommentsResponse {
    const message = createBaseListMemoCommentsResponse();
    message.memos = object.memos?.map((e) => Memo.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListMemoReactionsRequest(): ListMemoReactionsRequest {
  return { name: "" };
}

export const ListMemoReactionsRequest: MessageFns<ListMemoReactionsRequest> = {
  encode(message: ListMemoReactionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoReactionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoReactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoReactionsRequest>): ListMemoReactionsRequest {
    return ListMemoReactionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoReactionsRequest>): ListMemoReactionsRequest {
    const message = createBaseListMemoReactionsRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseListMemoReactionsResponse(): ListMemoReactionsResponse {
  return { reactions: [] };
}

export const ListMemoReactionsResponse: MessageFns<ListMemoReactionsResponse> = {
  encode(message: ListMemoReactionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.reactions) {
      Reaction.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListMemoReactionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListMemoReactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.reactions.push(Reaction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListMemoReactionsResponse>): ListMemoReactionsResponse {
    return ListMemoReactionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListMemoReactionsResponse>): ListMemoReactionsResponse {
    const message = createBaseListMemoReactionsResponse();
    message.reactions = object.reactions?.map((e) => Reaction.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUpsertMemoReactionRequest(): UpsertMemoReactionRequest {
  return { name: "", reaction: undefined };
}

export const UpsertMemoReactionRequest: MessageFns<UpsertMemoReactionRequest> = {
  encode(message: UpsertMemoReactionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.reaction !== undefined) {
      Reaction.encode(message.reaction, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpsertMemoReactionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpsertMemoReactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reaction = Reaction.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpsertMemoReactionRequest>): UpsertMemoReactionRequest {
    return UpsertMemoReactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpsertMemoReactionRequest>): UpsertMemoReactionRequest {
    const message = createBaseUpsertMemoReactionRequest();
    message.name = object.name ?? "";
    message.reaction = (object.reaction !== undefined && object.reaction !== null)
      ? Reaction.fromPartial(object.reaction)
      : undefined;
    return message;
  },
};

function createBaseDeleteMemoReactionRequest(): DeleteMemoReactionRequest {
  return { id: 0 };
}

export const DeleteMemoReactionRequest: MessageFns<DeleteMemoReactionRequest> = {
  encode(message: DeleteMemoReactionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMemoReactionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMemoReactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteMemoReactionRequest>): DeleteMemoReactionRequest {
    return DeleteMemoReactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteMemoReactionRequest>): DeleteMemoReactionRequest {
    const message = createBaseDeleteMemoReactionRequest();
    message.id = object.id ?? 0;
    return message;
  },
};

export type MemoServiceDefinition = typeof MemoServiceDefinition;
export const MemoServiceDefinition = {
  name: "MemoService",
  fullName: "memos.api.v1.MemoService",
  methods: {
    /** CreateMemo creates a memo. */
    createMemo: {
      name: "CreateMemo",
      requestType: CreateMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              21,
              58,
              4,
              109,
              101,
              109,
              111,
              34,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              101,
              109,
              111,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemos lists memos with pagination and filter. */
    listMemos: {
      name: "ListMemos",
      requestType: ListMemosRequest,
      requestStream: false,
      responseType: ListMemosResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              49,
              90,
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              117,
              115,
              101,
              114,
              115,
              47,
              42,
              125,
              47,
              109,
              101,
              109,
              111,
              115,
              18,
              13,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              109,
              101,
              109,
              111,
              115,
            ]),
          ],
        },
      },
    },
    /** GetMemo gets a memo. */
    getMemo: {
      name: "GetMemo",
      requestType: GetMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** UpdateMemo updates a memo. */
    updateMemo: {
      name: "UpdateMemo",
      requestType: UpdateMemoRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([16, 109, 101, 109, 111, 44, 117, 112, 100, 97, 116, 101, 95, 109, 97, 115, 107])],
          578365826: [
            new Uint8Array([
              35,
              58,
              4,
              109,
              101,
              109,
              111,
              50,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              109,
              101,
              109,
              111,
              46,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteMemo deletes a memo. */
    deleteMemo: {
      name: "DeleteMemo",
      requestType: DeleteMemoRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              24,
              42,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** RenameMemoTag renames a tag for a memo. */
    renameMemoTag: {
      name: "RenameMemoTag",
      requestType: RenameMemoTagRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              41,
              58,
              1,
              42,
              50,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              116,
              97,
              103,
              115,
              58,
              114,
              101,
              110,
              97,
              109,
              101,
            ]),
          ],
        },
      },
    },
    /** DeleteMemoTag deletes a tag for a memo. */
    deleteMemoTag: {
      name: "DeleteMemoTag",
      requestType: DeleteMemoTagRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              37,
              42,
              35,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              112,
              97,
              114,
              101,
              110,
              116,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              116,
              97,
              103,
              115,
              47,
              123,
              116,
              97,
              103,
              125,
            ]),
          ],
        },
      },
    },
    /** SetMemoResources sets resources for a memo. */
    setMemoResources: {
      name: "SetMemoResources",
      requestType: SetMemoResourcesRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              58,
              1,
              42,
              50,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoResources lists resources for a memo. */
    listMemoResources: {
      name: "ListMemoResources",
      requestType: ListMemoResourcesRequest,
      requestStream: false,
      responseType: ListMemoResourcesResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
            ]),
          ],
        },
      },
    },
    /** SetMemoRelations sets relations for a memo. */
    setMemoRelations: {
      name: "SetMemoRelations",
      requestType: SetMemoRelationsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              58,
              1,
              42,
              50,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              108,
              97,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoRelations lists relations for a memo. */
    listMemoRelations: {
      name: "ListMemoRelations",
      requestType: ListMemoRelationsRequest,
      requestStream: false,
      responseType: ListMemoRelationsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              108,
              97,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** CreateMemoComment creates a comment for a memo. */
    createMemoComment: {
      name: "CreateMemoComment",
      requestType: CreateMemoCommentRequest,
      requestStream: false,
      responseType: Memo,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              42,
              58,
              7,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              34,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoComments lists comments for a memo. */
    listMemoComments: {
      name: "ListMemoComments",
      requestType: ListMemoCommentsRequest,
      requestStream: false,
      responseType: ListMemoCommentsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              33,
              18,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              99,
              111,
              109,
              109,
              101,
              110,
              116,
              115,
            ]),
          ],
        },
      },
    },
    /** ListMemoReactions lists reactions for a memo. */
    listMemoReactions: {
      name: "ListMemoReactions",
      requestType: ListMemoReactionsRequest,
      requestStream: false,
      responseType: ListMemoReactionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** UpsertMemoReaction upserts a reaction for a memo. */
    upsertMemoReaction: {
      name: "UpsertMemoReaction",
      requestType: UpsertMemoReactionRequest,
      requestStream: false,
      responseType: Reaction,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              58,
              1,
              42,
              34,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              109,
              101,
              109,
              111,
              115,
              47,
              42,
              125,
              47,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    /** DeleteMemoReaction deletes a reaction for a memo. */
    deleteMemoReaction: {
      name: "DeleteMemoReaction",
      requestType: DeleteMemoReactionRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([2, 105, 100])],
          578365826: [
            new Uint8Array([
              24,
              42,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              114,
              101,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
              47,
              123,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
