// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/resource_service.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { HttpBody } from "../../google/api/httpbody";
import { Empty } from "../../google/protobuf/empty";
import { FieldMask } from "../../google/protobuf/field_mask";
import { Timestamp } from "../../google/protobuf/timestamp";

export const protobufPackage = "memos.api.v1";

export interface Resource {
  /**
   * The name of the resource.
   * Format: resources/{resource}, resource is the user defined if or uuid.
   */
  name: string;
  createTime?: Date | undefined;
  filename: string;
  content: Uint8Array;
  externalLink: string;
  type: string;
  size: number;
  /** The related memo. Refer to `Memo.name`. */
  memo?: string | undefined;
}

export interface CreateResourceRequest {
  resource?: Resource | undefined;
}

export interface ListResourcesRequest {
}

export interface ListResourcesResponse {
  resources: Resource[];
}

export interface GetResourceRequest {
  /** The name of the resource. */
  name: string;
}

export interface GetResourceBinaryRequest {
  /** The name of the resource. */
  name: string;
  /** The filename of the resource. Mainly used for downloading. */
  filename: string;
  /** A flag indicating if the thumbnail version of the resource should be returned */
  thumbnail: boolean;
}

export interface UpdateResourceRequest {
  resource?: Resource | undefined;
  updateMask?: string[] | undefined;
}

export interface DeleteResourceRequest {
  /** The name of the resource. */
  name: string;
}

function createBaseResource(): Resource {
  return {
    name: "",
    createTime: undefined,
    filename: "",
    content: new Uint8Array(0),
    externalLink: "",
    type: "",
    size: 0,
    memo: undefined,
  };
}

export const Resource: MessageFns<Resource> = {
  encode(message: Resource, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(26).fork()).join();
    }
    if (message.filename !== "") {
      writer.uint32(34).string(message.filename);
    }
    if (message.content.length !== 0) {
      writer.uint32(42).bytes(message.content);
    }
    if (message.externalLink !== "") {
      writer.uint32(50).string(message.externalLink);
    }
    if (message.type !== "") {
      writer.uint32(58).string(message.type);
    }
    if (message.size !== 0) {
      writer.uint32(64).int64(message.size);
    }
    if (message.memo !== undefined) {
      writer.uint32(74).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Resource {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResource();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.filename = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.content = reader.bytes();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.externalLink = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.size = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Resource>): Resource {
    return Resource.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Resource>): Resource {
    const message = createBaseResource();
    message.name = object.name ?? "";
    message.createTime = object.createTime ?? undefined;
    message.filename = object.filename ?? "";
    message.content = object.content ?? new Uint8Array(0);
    message.externalLink = object.externalLink ?? "";
    message.type = object.type ?? "";
    message.size = object.size ?? 0;
    message.memo = object.memo ?? undefined;
    return message;
  },
};

function createBaseCreateResourceRequest(): CreateResourceRequest {
  return { resource: undefined };
}

export const CreateResourceRequest: MessageFns<CreateResourceRequest> = {
  encode(message: CreateResourceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.resource !== undefined) {
      Resource.encode(message.resource, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateResourceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateResourceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resource = Resource.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<CreateResourceRequest>): CreateResourceRequest {
    return CreateResourceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateResourceRequest>): CreateResourceRequest {
    const message = createBaseCreateResourceRequest();
    message.resource = (object.resource !== undefined && object.resource !== null)
      ? Resource.fromPartial(object.resource)
      : undefined;
    return message;
  },
};

function createBaseListResourcesRequest(): ListResourcesRequest {
  return {};
}

export const ListResourcesRequest: MessageFns<ListResourcesRequest> = {
  encode(_: ListResourcesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListResourcesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListResourcesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListResourcesRequest>): ListResourcesRequest {
    return ListResourcesRequest.fromPartial(base ?? {});
  },
  fromPartial(_: DeepPartial<ListResourcesRequest>): ListResourcesRequest {
    const message = createBaseListResourcesRequest();
    return message;
  },
};

function createBaseListResourcesResponse(): ListResourcesResponse {
  return { resources: [] };
}

export const ListResourcesResponse: MessageFns<ListResourcesResponse> = {
  encode(message: ListResourcesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.resources) {
      Resource.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListResourcesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListResourcesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resources.push(Resource.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<ListResourcesResponse>): ListResourcesResponse {
    return ListResourcesResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ListResourcesResponse>): ListResourcesResponse {
    const message = createBaseListResourcesResponse();
    message.resources = object.resources?.map((e) => Resource.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetResourceRequest(): GetResourceRequest {
  return { name: "" };
}

export const GetResourceRequest: MessageFns<GetResourceRequest> = {
  encode(message: GetResourceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetResourceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetResourceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetResourceRequest>): GetResourceRequest {
    return GetResourceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetResourceRequest>): GetResourceRequest {
    const message = createBaseGetResourceRequest();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGetResourceBinaryRequest(): GetResourceBinaryRequest {
  return { name: "", filename: "", thumbnail: false };
}

export const GetResourceBinaryRequest: MessageFns<GetResourceBinaryRequest> = {
  encode(message: GetResourceBinaryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.filename !== "") {
      writer.uint32(18).string(message.filename);
    }
    if (message.thumbnail !== false) {
      writer.uint32(24).bool(message.thumbnail);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetResourceBinaryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetResourceBinaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.filename = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.thumbnail = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<GetResourceBinaryRequest>): GetResourceBinaryRequest {
    return GetResourceBinaryRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetResourceBinaryRequest>): GetResourceBinaryRequest {
    const message = createBaseGetResourceBinaryRequest();
    message.name = object.name ?? "";
    message.filename = object.filename ?? "";
    message.thumbnail = object.thumbnail ?? false;
    return message;
  },
};

function createBaseUpdateResourceRequest(): UpdateResourceRequest {
  return { resource: undefined, updateMask: undefined };
}

export const UpdateResourceRequest: MessageFns<UpdateResourceRequest> = {
  encode(message: UpdateResourceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.resource !== undefined) {
      Resource.encode(message.resource, writer.uint32(10).fork()).join();
    }
    if (message.updateMask !== undefined) {
      FieldMask.encode(FieldMask.wrap(message.updateMask), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateResourceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateResourceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.resource = Resource.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateMask = FieldMask.unwrap(FieldMask.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<UpdateResourceRequest>): UpdateResourceRequest {
    return UpdateResourceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateResourceRequest>): UpdateResourceRequest {
    const message = createBaseUpdateResourceRequest();
    message.resource = (object.resource !== undefined && object.resource !== null)
      ? Resource.fromPartial(object.resource)
      : undefined;
    message.updateMask = object.updateMask ?? undefined;
    return message;
  },
};

function createBaseDeleteResourceRequest(): DeleteResourceRequest {
  return { name: "" };
}

export const DeleteResourceRequest: MessageFns<DeleteResourceRequest> = {
  encode(message: DeleteResourceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteResourceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteResourceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<DeleteResourceRequest>): DeleteResourceRequest {
    return DeleteResourceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteResourceRequest>): DeleteResourceRequest {
    const message = createBaseDeleteResourceRequest();
    message.name = object.name ?? "";
    return message;
  },
};

export type ResourceServiceDefinition = typeof ResourceServiceDefinition;
export const ResourceServiceDefinition = {
  name: "ResourceService",
  fullName: "memos.api.v1.ResourceService",
  methods: {
    /** CreateResource creates a new resource. */
    createResource: {
      name: "CreateResource",
      requestType: CreateResourceRequest,
      requestStream: false,
      responseType: Resource,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([
              29,
              58,
              8,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              34,
              17,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
            ]),
          ],
        },
      },
    },
    /** ListResources lists all resources. */
    listResources: {
      name: "ListResources",
      requestType: ListResourcesRequest,
      requestStream: false,
      responseType: ListResourcesResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365826: [
            new Uint8Array([19, 18, 17, 47, 97, 112, 105, 47, 118, 49, 47, 114, 101, 115, 111, 117, 114, 99, 101, 115]),
          ],
        },
      },
    },
    /** GetResource returns a resource by name. */
    getResource: {
      name: "GetResource",
      requestType: GetResourceRequest,
      requestStream: false,
      responseType: Resource,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              28,
              18,
              26,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** GetResourceBinary returns a resource binary by name. */
    getResourceBinary: {
      name: "GetResourceBinary",
      requestType: GetResourceBinaryRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([13, 110, 97, 109, 101, 44, 102, 105, 108, 101, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              37,
              18,
              35,
              47,
              102,
              105,
              108,
              101,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
              47,
              42,
              125,
              47,
              123,
              102,
              105,
              108,
              101,
              110,
              97,
              109,
              101,
              125,
            ]),
          ],
        },
      },
    },
    /** UpdateResource updates a resource. */
    updateResource: {
      name: "UpdateResource",
      requestType: UpdateResourceRequest,
      requestStream: false,
      responseType: Resource,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [
            new Uint8Array([
              20,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              44,
              117,
              112,
              100,
              97,
              116,
              101,
              95,
              109,
              97,
              115,
              107,
            ]),
          ],
          578365826: [
            new Uint8Array([
              47,
              58,
              8,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              50,
              35,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              46,
              110,
              97,
              109,
              101,
              61,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
    /** DeleteResource deletes a resource by name. */
    deleteResource: {
      name: "DeleteResource",
      requestType: DeleteResourceRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          8410: [new Uint8Array([4, 110, 97, 109, 101])],
          578365826: [
            new Uint8Array([
              28,
              42,
              26,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              123,
              110,
              97,
              109,
              101,
              61,
              114,
              101,
              115,
              111,
              117,
              114,
              99,
              101,
              115,
              47,
              42,
              125,
            ]),
          ],
        },
      },
    },
  },
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
