// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/reaction_service.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "memos.api.v1";

export interface Reaction {
  id: number;
  /**
   * The name of the creator.
   * Format: users/{user}
   */
  creator: string;
  /**
   * The content identifier.
   * For memo, it should be the `Memo.name`.
   */
  contentId: string;
  reactionType: string;
}

function createBaseReaction(): Reaction {
  return { id: 0, creator: "", contentId: "", reactionType: "" };
}

export const Reaction: MessageFns<Reaction> = {
  encode(message: Reaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    if (message.creator !== "") {
      writer.uint32(18).string(message.creator);
    }
    if (message.contentId !== "") {
      writer.uint32(26).string(message.contentId);
    }
    if (message.reactionType !== "") {
      writer.uint32(34).string(message.reactionType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Reaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.creator = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.contentId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.reactionType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<Reaction>): Reaction {
    return Reaction.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Reaction>): Reaction {
    const message = createBaseReaction();
    message.id = object.id ?? 0;
    message.creator = object.creator ?? "";
    message.contentId = object.contentId ?? "";
    message.reactionType = object.reactionType ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
