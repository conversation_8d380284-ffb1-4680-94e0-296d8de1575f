// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: api/v1/common.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "memos.api.v1";

export enum State {
  STATE_UNSPECIFIED = "STATE_UNSPECIFIED",
  NORMAL = "NORMAL",
  ARCHIVED = "ARCHIVED",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function stateFromJSON(object: any): State {
  switch (object) {
    case 0:
    case "STATE_UNSPECIFIED":
      return State.STATE_UNSPECIFIED;
    case 1:
    case "NORMAL":
      return State.NORMAL;
    case 2:
    case "ARCHIVED":
      return State.ARCHIVED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return State.UNRECOGNIZED;
  }
}

export function stateToNumber(object: State): number {
  switch (object) {
    case State.STATE_UNSPECIFIED:
      return 0;
    case State.NORMAL:
      return 1;
    case State.ARCHIVED:
      return 2;
    case State.UNRECOGNIZED:
    default:
      return -1;
  }
}

export enum Direction {
  DIRECTION_UNSPECIFIED = "DIRECTION_UNSPECIFIED",
  ASC = "ASC",
  DESC = "DESC",
  UNRECOGNIZED = "UNRECOGNIZED",
}

export function directionFromJSON(object: any): Direction {
  switch (object) {
    case 0:
    case "DIRECTION_UNSPECIFIED":
      return Direction.DIRECTION_UNSPECIFIED;
    case 1:
    case "ASC":
      return Direction.ASC;
    case 2:
    case "DESC":
      return Direction.DESC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Direction.UNRECOGNIZED;
  }
}

export function directionToNumber(object: Direction): number {
  switch (object) {
    case Direction.DIRECTION_UNSPECIFIED:
      return 0;
    case Direction.ASC:
      return 1;
    case Direction.DESC:
      return 2;
    case Direction.UNRECOGNIZED:
    default:
      return -1;
  }
}

/** Used internally for obfuscating the page token. */
export interface PageToken {
  limit: number;
  offset: number;
}

function createBasePageToken(): PageToken {
  return { limit: 0, offset: 0 };
}

export const PageToken: MessageFns<PageToken> = {
  encode(message: PageToken, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.limit !== 0) {
      writer.uint32(8).int32(message.limit);
    }
    if (message.offset !== 0) {
      writer.uint32(16).int32(message.offset);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageToken {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageToken();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.limit = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.offset = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create(base?: DeepPartial<PageToken>): PageToken {
    return PageToken.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<PageToken>): PageToken {
    const message = createBasePageToken();
    message.limit = object.limit ?? 0;
    message.offset = object.offset ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create(base?: DeepPartial<T>): T;
  fromPartial(object: DeepPartial<T>): T;
}
