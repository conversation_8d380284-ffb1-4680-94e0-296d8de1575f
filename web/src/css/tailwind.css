@import url("highlight.js/styles/github.css") (prefers-color-scheme: light);
@import url("highlight.js/styles/github-dark.css") (prefers-color-scheme: dark);

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .word-break {
    overflow-wrap: anywhere;
    word-break: normal;
  }
}

html.dark {
  color-scheme: dark;
}
html.light {
  color-scheme: light;
}
