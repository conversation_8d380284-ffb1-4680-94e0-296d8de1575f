{"auth": {"create-your-account": "Fiók létrehozása", "host-tip": "A weboldal rendszergazdájaként regisztrál.", "new-password": "<PERSON><PERSON>", "repeat-new-password": "<PERSON><PERSON><PERSON><PERSON>", "sign-in-tip": "<PERSON><PERSON><PERSON>?", "sign-up-tip": "Még nincs fió<PERSON>?"}, "common": {"about": "Névjegy", "admin": "Admin", "archive": "Archiválás", "archived": "Archívum", "avatar": "Avatar", "basic": "<PERSON><PERSON>lán<PERSON>", "beta": "<PERSON><PERSON><PERSON>", "cancel": "M<PERSON>gs<PERSON>", "change": "Módosítás", "clear": "Törlés", "close": "Bezárás", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "Létrehozás", "database": "Adatb<PERSON><PERSON><PERSON>", "days": "Napok", "delete": "Törlés", "description": "Le<PERSON><PERSON><PERSON>", "edit": "Szerkesztés", "email": "Email", "explore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Szűrő", "home": "Főoldal", "image": "<PERSON><PERSON><PERSON>", "inbox": "Értesítések", "language": "Nyelv", "learn-more": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "Hivatkozás", "mark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memos": "Jegyzetek", "name": "Név", "new": "<PERSON><PERSON>", "nickname": "Be<PERSON>név", "or": "vagy", "password": "Je<PERSON><PERSON><PERSON>", "pin": "Kitűzés", "pinned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preview": "Előnézet", "profile": "Profil", "remember-me": "Megjegyzés", "rename": "Átnevezés", "reset": "Visszaállítás", "resources": "Állományok", "restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Jogos<PERSON>ság", "save": "Men<PERSON>s", "search": "Keresés", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Beállítások", "share": "Megosztás", "sign-in": "Bejelentkezés", "sign-in-with": "Bejelentkezés {{provider}} használatával", "sign-out": "Kijelentkezés", "sign-up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statistics": "Statisztikák", "tags": "Címkék", "title": "Cím", "type": "<PERSON><PERSON><PERSON>", "unpin": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload": "Feltöltés", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON><PERSON>", "visibility": "<PERSON><PERSON><PERSON><PERSON>", "yourself": "Magad"}, "days": {"fri": "Pé", "mon": "Hé", "sat": "<PERSON><PERSON>", "sun": "Vas", "thu": "Csü", "tue": "<PERSON>", "wed": "Sze"}, "editor": {"add-your-comment-here": "Írd ide a megjegyzésed...", "any-thoughts": "<PERSON><PERSON>rmi ami a fejedben jár...", "save": "Men<PERSON>s"}, "inbox": {"memo-comment": "{{user}} ho<PERSON><PERSON><PERSON><PERSON><PERSON> ehhez: {{memo}}.", "version-update": "<PERSON><PERSON> {{version}} verzi<PERSON> már <PERSON>!"}, "memo": {"archived-at": "Archiválva:", "comment": {"self": "Hozzászólások"}, "copy-link": "Hivatkozás másolása", "count-memos-in-date": "{{count}} jeg<PERSON><PERSON>t ezen a napon: {{date}}", "delete-confirm": "Biztosan törlöd ezt a jegyzetet? EZ A MŰVELET VÉGLEGES", "no-archived-memos": "Nincsenek archivált jegyzetek.", "remove-completed-task-list-items": "<PERSON><PERSON><PERSON><PERSON><PERSON> el a kész", "remove-completed-task-list-items-confirm": "Biztos, hogy törölni akarod az összes kész feladatot? (Ez az akció v<PERSON>zavonhatatlan)", "search-placeholder": "Jegyzetek keresése", "show-less": "<PERSON><PERSON><PERSON>", "show-more": "<PERSON><PERSON><PERSON> muta<PERSON>", "view-detail": "Részletek", "visibility": {"disabled": "A nyilvános jegyzetek le vannak tiltva", "private": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protected": "<PERSON><PERSON><PERSON><PERSON>", "public": "N<PERSON>lván<PERSON>"}}, "message": {"archived-successfully": "Sikeres archiválás", "change-memo-created-time": "Jegyzet létrehozási idejének változtatása", "copied": "M<PERSON>ol<PERSON>", "deleted-successfully": "<PERSON><PERSON><PERSON> t<PERSON>", "fill-all": "Töltsd ki az összes mezőt.", "maximum-upload-size-is": "A maximálisan megengedett feltöltési méret {{size}} MiB", "memo-not-found": "Jegyzet nem található.", "new-password-not-match": "<PERSON>z új jelszavak nem egyeznek.", "no-data": "<PERSON><PERSON> adat.", "password-changed": "Jelszó megváltoztatva", "password-not-match": "A jelszavak nem egyeznek.", "remove-completed-task-list-items-successfully": "Sikeresen eltávolítva!", "restored-successfully": "<PERSON><PERSON><PERSON> v<PERSON>zaállítás", "succeed-copy-link": "Hivatkoz<PERSON> si<PERSON>esen másol<PERSON>.", "update-succeed": "<PERSON><PERSON><PERSON> fris<PERSON>", "user-not-found": "Felhasz<PERSON><PERSON><PERSON> nem található"}, "reference": {"add-references": "Referenciák hozzáadása", "embedded-usage": "Használat mint beépülő tartalom", "no-memos-found": "<PERSON><PERSON> j<PERSON>"}, "resource": {"clear": "T<PERSON>r<PERSON><PERSON>", "copy-link": "Hivatkozás másolása", "create-dialog": {"external-link": {"file-name": "Fájlnév", "file-name-placeholder": "Fájlnév", "link": "Link", "link-placeholder": "https://a.fajlhoz.mutato/link", "option": "Külső link", "type": "<PERSON><PERSON><PERSON>", "type-placeholder": "Fáj<PERSON><PERSON><PERSON>"}, "local-file": {"choose": "Fájl kiválasztása…", "option": "<PERSON><PERSON><PERSON>"}, "title": "Állomány létrehozása", "upload-method": "Feltöltési mód"}, "delete-resource": "Állomány törlése", "delete-selected-resources": "Kijelölt állományok törlése", "fetching-data": "Adatok <PERSON>…", "file-drag-drop-prompt": "Húzd ide a fájlt a feltöltéshez", "linked-amount": "Hivatkozások száma", "no-files-selected": "Nincsenek kijelölt fájlok", "no-resources": "<PERSON><PERSON><PERSON><PERSON>.", "no-unused-resources": "<PERSON>ncsenek nem használt <PERSON>", "reset-link": "Hivatkozás visszaállítása", "reset-link-prompt": "Biztosan v<PERSON>zaállítod ezt a hivatkozást? Ez minden létező linket érvénytelenít. EZ A MŰVELET VÉGLEGES", "reset-resource-link": "Állomány hivatkozásának visszaállítása"}, "router": {"back-to-top": "Vissza az oldal tetejére", "go-to-home": "Vissza a főoldalra"}, "setting": {"account-section": {"change-password": "Jelszó megváltoztatása", "email-note": "<PERSON><PERSON>", "export-memos": "Jegyzetek exportálása", "nickname-note": "A <PERSON>ben me<PERSON>", "openapi-reset": "OpenAPI kulcs visszaállítása", "openapi-sample-post": "Hello #memos innen: {{url}}", "openapi-title": "OpenAPI", "reset-api": "API visszaállítása", "title": "Fiókinformáció", "update-information": "Informá<PERSON><PERSON> f<PERSON>", "username-note": "Bejelentkez<PERSON><PERSON><PERSON>"}, "appearance-option": {"dark": "<PERSON><PERSON>", "light": "<PERSON><PERSON> v<PERSON>", "system": "Rendszer követése"}, "member": "Tag", "member-list": "Taglista", "member-section": {"archive-member": "Tag archiválása", "archive-warning": "Biztosan archiválod {{username}} tagot?", "create-a-member": "Tag létrehozása", "delete-member": "Tag törlése", "delete-warning": "Biztosan tö<PERSON>öd {{username}} tagot? EZ A MŰVELET VÉGLEGES"}, "my-account": "Fiókom", "preference": "Prefer<PERSON><PERSON><PERSON>", "preference-section": {"default-memo-sort-option": "Jegyzet létrehozási ideje", "default-memo-visibility": "Jegyzetek alapértelmezett láthatósága", "theme": "<PERSON><PERSON><PERSON>"}, "sso": "SSO", "sso-section": {"authorization-endpoint": "Hitelesítési végpont", "client-id": "Kliens ID", "client-secret": "<PERSON><PERSON><PERSON> t<PERSON>", "confirm-delete": "Biztosan törlöd a(z) \"{{name}}\" nevű SSO konfigurációt? EZ A MŰVELET VÉGLEGES", "create-sso": "SSO létrehozása", "custom": "<PERSON><PERSON><PERSON><PERSON>", "delete-sso": "Törlés megerősítése", "disabled-password-login-warning": "Mivel a j<PERSON>zavas bejelent<PERSON> tilt<PERSON>, fokozott figyelem szükséges identitásszolgáltatók eltávolítása során", "display-name": "Megjelenített név", "identifier": "Azonosító", "identifier-filter": "Azonosító s<PERSON>", "redirect-url": "Átirányítási URL", "scopes": "Hatókörök", "sso-created": "{{name}} nevű SSO létrehozva", "sso-list": "SSO lista", "sso-updated": "{{name}} nevű SSO frissítve", "template": "Sablon", "token-endpoint": "Token végpont", "update-sso": "SSO frissítése", "user-endpoint": "Felhasználói végpont"}, "storage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storage-section": {"accesskey": "Hozzáférési kulcs", "accesskey-placeholder": "Access key / Access ID", "bucket": "Bucket", "bucket-placeholder": "Bucket neve", "create-a-service": "Szolgáltatás létrehozása", "create-storage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current-storage": "Jelenlegi objektumtár", "delete-storage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endpoint": "<PERSON><PERSON>g<PERSON>", "local-storage-path": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path-description": "Használhatod ugyanazokat a dinamikus változókat a helyi tá<PERSON>hel<PERSON>ől, mint a {filename}", "path-placeholder": "egyedi/útvonal", "region": "<PERSON><PERSON><PERSON><PERSON>", "region-placeholder": "Régió neve", "s3-compatible-url": "S3 kompatibilis URL", "secretkey": "Titkos k<PERSON>", "secretkey-placeholder": "Secret key / Access Key", "storage-services": "T<PERSON>rhelyszolgáltatások", "type-database": "Adatb<PERSON><PERSON><PERSON>", "type-local": "<PERSON><PERSON><PERSON>", "update-a-service": "Szolgáltatás módosítása", "update-local-path": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> módosítása", "update-local-path-description": "A helyi tárhely útvonala egy relatív útvonal az adatbázis fájlhoz", "update-storage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url-prefix": "URL prefix", "url-prefix-placeholder": "Egyedi URL prefix, nem kötelező", "url-suffix": "URL suffix", "url-suffix-placeholder": "Egyedi URL suffix, nem kötelező", "warning-text": "Biztosan törlöd a(z) \"{{name}}\" t<PERSON><PERSON><PERSON>yszolgáltatást? EZ A MŰVELET VÉGLEGES"}, "system": "Rendszer", "system-section": {"additional-script": "Egyedi script", "additional-script-placeholder": "Egyedi JavaScript kód", "additional-style": "<PERSON><PERSON><PERSON><PERSON> st<PERSON>lus", "additional-style-placeholder": "Egyedi CSS kód", "allow-user-signup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> engedélyezése", "customize-server": {"appearance": "Szerver témája", "description": "Le<PERSON><PERSON><PERSON>", "icon-url": "Ikon URL", "locale": "Szerver nyelve", "title": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "disable-password-login": "Jelszavas bejelentkezés letiltása", "disable-password-login-final-warning": "<PERSON><PERSON> be, hogy \"CONFIRM\" ha tudod mit csinálsz.", "disable-password-login-warning": "Ez letiltja a jelszavas bejelentkezést minden felhasználó számára. Ha a konfigurált identitásszolgáltatók nem elérhetőek, a bejelentkezés nem lehetséges, ezen beállítás kikapcsolása nélkül az adatbázisban. Ezen kívül fokozott figyelem szükséges identitásszolgálók eltávolítása során is", "disable-public-memos": "Nyilvános jegyzetek letiltása", "display-with-updated-time": "Megjelení<PERSON>s frissített <PERSON>", "enable-password-login": "Jelszavas be<PERSON>lentkezés engedélyezése", "enable-password-login-warning": "Ez engedélyezi a jelszavas bejelentkezést minden felhasználó számára. Csak akkor foly<PERSON>, ha szeretnéd, ha a felhasználók SSO és j<PERSON><PERSON><PERSON> is be tudjanak jele<PERSON>", "max-upload-size": "<PERSON><PERSON><PERSON> (MiB)", "max-upload-size-hint": "Az ajánlott érték 32 MiB.", "removed-completed-task-list-items": "Kikapcsold a kész törölését", "server-name": "Szerver neve"}}, "tag": {"all-tags": "<PERSON><PERSON> c<PERSON>", "create-tag": "Címke létrehozása", "create-tags-guide": "Létrehozhatsz címkéket `#címke` be<PERSON><PERSON><PERSON><PERSON><PERSON>.", "delete-confirm": "Biztosan törlöd ezt a címkét?", "delete-tag": "Címke törlése", "no-tag-found": "<PERSON><PERSON> c<PERSON>"}}