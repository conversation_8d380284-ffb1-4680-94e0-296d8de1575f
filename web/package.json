{"name": "memos", "scripts": {"dev": "vite", "build": "vite build", "release": "vite build --mode release --outDir=../server/router/frontend/dist --emptyOutDir", "lint": "tsc --noEmit --skipLibCheck && eslint --ext .js,.ts,.tsx, src"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@github/relative-time-element": "^4.4.5", "@matejmazur/react-katex": "^3.1.3", "@mui/joy": "5.0.0-beta.51", "@radix-ui/react-popover": "^1.1.6", "@usememos/mui": "0.1.0-20250515140125", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "fuse.js": "^7.1.0", "highlight.js": "^11.11.1", "i18next": "^24.2.3", "katex": "^0.16.21", "leaflet": "^1.9.4", "lodash-es": "^4.17.21", "lucide-react": "^0.486.0", "mermaid": "^11.4.1", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "react": "^18.3.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-force-graph-2d": "^1.27.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-leaflet": "^4.2.1", "react-router-dom": "^7.3.0", "react-simple-pull-to-refresh": "^1.3.3", "react-use": "^17.6.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "textarea-caret": "^3.1.0", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@bufbuild/protobuf": "^2.2.5", "@eslint/js": "^9.23.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/d3": "^7.4.3", "@types/katex": "^0.16.7", "@types/leaflet": "^1.9.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.3", "@types/qs": "^6.9.18", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/textarea-caret": "^3.0.3", "@types/uuid": "^10.0.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "code-inspector-plugin": "^0.18.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "long": "^5.3.1", "nice-grpc-web": "^3.3.7", "postcss": "^8.5.3", "prettier": "^3.5.3", "terser": "^5.39.0", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0", "vite": "^6.2.1"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}