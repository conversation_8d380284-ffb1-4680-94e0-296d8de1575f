name: Frontend Test

on:
  push:
    branches: [main]
  pull_request:
    branches:
      - main
    paths:
      - "web/**"

jobs:
  static-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4.1.0
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: pnpm
          cache-dependency-path: "web/pnpm-lock.yaml"
      - run: pnpm install
        working-directory: web
      - name: Run check
        run: pnpm lint
        working-directory: web

  frontend-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4.1.0
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: pnpm
          cache-dependency-path: "web/pnpm-lock.yaml"
      - run: pnpm install
        working-directory: web
      - name: Run frontend build
        run: pnpm build
        working-directory: web
