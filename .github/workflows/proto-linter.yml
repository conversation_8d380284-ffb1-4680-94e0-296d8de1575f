name: <PERSON><PERSON><PERSON>

on:
  push:
    branches: [main]
  pull_request:
    branches:
      - main
    paths:
      - "proto/**"

jobs:
  lint-protos:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup buf
        uses: bufbuild/buf-setup-action@v1
        with:
          github_token: ${{ github.token }}
      - name: buf lint
        uses: bufbuild/buf-lint-action@v1
        with:
          input: "proto"
      - name: buf format
        run: |
          if [[ $(buf format -d) ]]; then
            echo "Run 'buf format -d'"
            exit 1
          fi
