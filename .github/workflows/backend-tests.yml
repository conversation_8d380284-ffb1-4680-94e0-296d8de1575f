name: Backend Test

on:
  push:
    branches: [main]
  pull_request:
    branches:
      - main
    paths:
      - "go.mod"
      - "go.sum"
      - "**.go"

jobs:
  go-static-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.24
          check-latest: true
          cache: true
      - name: Verify go.mod is tidy
        run: |
          go mod tidy -go=1.24
          git diff --exit-code
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v7
        with:
          version: v2.0.2
          args: --verbose --timeout=3m
          skip-cache: true

  go-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.24
          check-latest: true
          cache: true
      - name: Run all tests
        run: go test -v ./... | tee test.log; exit ${PIPESTATUS[0]}
      - name: Pretty print tests running time
        run: grep --color=never -e '--- PASS:' -e '--- FAIL:' test.log | sed 's/[:()]//g' | awk '{print $2,$3,$4}' | sort -t' ' -nk3 -r | awk '{sum += $3; print $1,$2,$3,sum"s"}'
